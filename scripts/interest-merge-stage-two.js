/*
Need to create this index in atlas search from interest collection:
Index name: interest_translations
{
  "mappings": {
    "dynamic": false,
    "fields": {
      "translations": {
        "dynamic": false,
        "fields": {
          "name": {
            "type": "autocomplete"
          }
        },
        "type": "document"
      }
    }
  }
}

Need to create this vector index in atlas search from interest collection:
Index name: interest_vector
{
  "fields": [
    {
      "numDimensions": 256,
      "path": "embedding",
      "similarity": "cosine",
      "type": "vector"
    }
  ]
}

ENV Required: MONGODB_URI, MONGODB_URI_RECORDS, OPENAI_KEY, Google Translations Access
*/

const mongoose = require('mongoose');
const Interest = require('../models/interest');
const { generateVector, validateMerge, validateInterestSimilarity, translateInterest, mergeInterests } = require('../lib/interest-merge/helper');
const { cleanInterestName } = require('../lib/interest');
const { detectLanguage } = require('../lib/detect-language');

const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/test';
const BATCH_SIZE = 50; // Process 50 interests at a time for openai operations
let TOTAL_PROCESSED = 0;

const fetchSimilarInterests = async (interestName, languageCode = 'en') => {
  const vector = await generateVector(interestName);
  if (!vector) return { queryVector: null, similarInterests: [] };

  try {
    const vectorSearchResults = await Interest.aggregate([
      {
        $vectorSearch: {
          index: 'interest_vector',
          queryVector: vector,
          path: 'embedding',
          numCandidates: 10000,
          limit: 50,
        },
      },
      { $addFields: { score: { $meta: 'vectorSearchScore' } } },
      { $match: { score: { $gte: 0.65 } } },
      {
        $project: {
          name: 1,
          score: 1,
          translations: 1,
          _id: 0,
        },
      },
    ]);

    const uniqueTranslations = new Map();

    vectorSearchResults.forEach(({ name, score, translations }) => {
      const translatedName = translations.find((t) => t.languageCode === languageCode)?.name;
      if (translatedName && !uniqueTranslations.has(translatedName)) {
        uniqueTranslations.set(translatedName, { name, score, translation: translatedName });
      }
    });

    const similarInterests = Array.from(uniqueTranslations.values()).slice(0, 30);

    return { queryVector: vector, similarInterests };
  } catch (error) {
    console.log('Error fetching similar interests:', error);
    return { queryVector: null, similarInterests: [] };
  }
};

const mergeInterestIntoTarget = async (sourceInterest, targetName) => {
  try {
    const targetInterest = await Interest.findOne({ name: targetName });

    // Find other interests that were previously merged into the current source
    const previouslyMerged = await Interest.find({
      mergedWith: {
        _id: sourceInterest._id.toString(),
        name: sourceInterest.name,
      },
    });

    // Reassign their mergedWith reference to the final target
    if (previouslyMerged.length) {
      const previouslyMergedIds = previouslyMerged.map((interest) => interest._id);
      await Interest.updateMany(
        { _id: { $in: previouslyMergedIds } },
        {
          $set: {
            mergedWith: {
              _id: targetInterest._id.toString(),
              name: targetName,
            },
          },
        },
      );
    }

    targetInterest.category ||= sourceInterest.category;
    targetInterest.libCategory ||= sourceInterest.libCategory;
    targetInterest.numQuestions += sourceInterest.numQuestions || 0;
    targetInterest.numFollowers += sourceInterest.numFollowers || 0;
    await targetInterest.save();

    // Mark the source interest as processed and merged
    sourceInterest.processedDuringMerge = true;
    sourceInterest.translations = undefined;
    sourceInterest.embedding = undefined;
    sourceInterest.mergedWith = {
      _id: targetInterest._id.toString(),
      name: targetName,
    };
    await sourceInterest.save();

    // Will re-calculate after the merge
    // Update related collections that reference the old interest name
    // await mergeInterestCountryCounts(targetName, [sourceInterest.name]);
    // await InterestPoint.updateMany({ interest: sourceInterest.name }, { $set: { interest: targetName } });

    console.log(`Merged ${sourceInterest.name} into ${targetName} via similarity check`);
  } catch (error) {
    console.log(`Failed to merge "${sourceInterest.name}" into "${targetName}":`, error);
  }
};

const translateAndSaveInterest = async (interest, queryVector) => {
  try {
    // Skip reprocessing if translations already exist
    if (interest.translations?.length) {
      interest.processedDuringMerge = true;
      interest.embedding = queryVector;
      await interest.save();
      return;
    }

    // Translate interest name
    const translatedNames = await translateInterest(interest.name, []);
    if (!translatedNames) {
      console.log(`Error translating interest: "${interest.name}"`);
      return;
    }

    const cleanedTranslations = Object.fromEntries(Object.entries(translatedNames).map(([lang, value]) => [lang, cleanInterestName(value)]));
    const cleanedEnglishName = cleanedTranslations.en;

    // If English name matches original, just attach translations
    if (interest.name === cleanedEnglishName) {
      interest.translations = Object.entries(cleanedTranslations).map(([languageCode, name]) => ({ languageCode, name }));
      interest.embedding = queryVector;
      interest.processedDuringMerge = true;
      await interest.save();
      return;
    }

    // Look for existing interest by cleaned English name
    let existingInterest = await Interest.findOne({ name: cleanedEnglishName });
    const isNewInterest = !existingInterest;

    if (isNewInterest) {
      existingInterest = new Interest({
        name: cleanedEnglishName,
        interest: `#${cleanedEnglishName}`,
        status: null,
        translations: Object.entries(cleanedTranslations).map(([languageCode, name]) => ({ languageCode, name })),
      });
      console.log(`Creating new interest: "${cleanedEnglishName}" (from "${interest.name}")`);
    } else {
      // check if the existing interest is merged with another interest
      if (existingInterest.mergedWith) {
        existingInterest = await Interest.findOne({ _id: existingInterest.mergedWith._id });
      }
    }

    // Merge current interest into existing
    mergeInterests(existingInterest, interest);
    // Handle existing interest persistence
    if (!existingInterest.processedDuringMerge) {
      existingInterest.status = null;
    }
    await existingInterest.save();
    console.log(`Merged "${interest.name}" into "${existingInterest.name}"`);

    // Mark source interest as merged
    interest.processedDuringMerge = true;
    interest.translations = undefined;
    interest.embedding = undefined;
    interest.mergedWith = {
      _id: existingInterest._id.toString(),
      name: existingInterest.name,
    };
    await interest.save();

    // Will re-calculate after the merge
    // Update related collections
    // const updatedName = interest.mergedWith.name;
    // await mergeInterestCountryCounts(updatedName, [interest.name]);
    // await InterestPoint.updateMany({ interest: interest.name }, { $set: { interest: updatedName } });
  } catch (error) {
    console.log(`Error in translateAndSaveInterest for "${interest.name}":`, error);
  }
};

const processSingleInterest = async (interest) => {
  console.log('Processing interest:', interest.name);

  try {
    let language = interest.language || (await detectLanguage(interest.name))?.language;
    if (!language || language === 'und') language = 'en';

    const { queryVector, similarInterests } = await fetchSimilarInterests(interest.name, language);
    if (!similarInterests.length) {
      await translateAndSaveInterest(interest, queryVector);
      ++TOTAL_PROCESSED;
      return;
    }

    // Disable merging based on semantic similarity only
    // const similarMatch = similarInterests.find((i) => i.score >= 0.9);
    // if (similarMatch) {
    //   await mergeInterestIntoTarget(interest, similarMatch.name);
    //   ++TOTAL_PROCESSED;
    //   return;
    // }

    // Validate similarity with AI
    const translations = similarInterests.map((i) => i.translation);
    const result = await validateInterestSimilarity(interest.name, translations);
    if (!result?.similar) {
      await translateAndSaveInterest(interest, queryVector);
      ++TOTAL_PROCESSED;
      return;
    }

    // Validate merge decision
    const mergeValidation = await validateMerge(interest.name, result.similar);
    const isSame = mergeValidation?.toLowerCase().includes('same');
    if (isSame) {
      const similar = similarInterests.find((i) => i.translation === result.similar);
      if (similar) {
        await mergeInterestIntoTarget(interest, similar.name);
      }
    } else {
      await translateAndSaveInterest(interest, queryVector);
    }

    ++TOTAL_PROCESSED;
  } catch (error) {
    console.log(`Error processing interest: ${interest.name}`, error);
  }
};

// This script pulls and process interests from the database with more than 0 numFollowers and 0 numQuestions
(async () => {
  try {
    await mongoose.connect(MONGODB_URI);
    console.log('Connected to MongoDB');

    let hasMoreData = true;
    let lastProcessedId = null;

    while (hasMoreData) {
      const query = {
        $or: [{ numFollowers: { $gt: 0 } }, { numQuestions: { $gt: 0 } }],
        processedDuringMerge: { $exists: false },
        status: null,
      };

      if (lastProcessedId) {
        query._id = { $gt: lastProcessedId };
      }

      const batch = await Interest.find(query).sort({ _id: 1 }).limit(BATCH_SIZE);

      if (batch.length === 0) {
        hasMoreData = false;
        break;
      }

      await Promise.all(batch.map(processSingleInterest));
      lastProcessedId = batch[batch.length - 1]._id;
      TOTAL_PROCESSED += batch.length;
      console.log('Total Processed:', TOTAL_PROCESSED);
    }
  } catch (error) {
    console.log('Error processing remaining interests', error);
  } finally {
    console.log('Complete processing remaining approved interests');
    await mongoose.disconnect();
    console.log('Disconnected from MongoDB');
  }
})();
