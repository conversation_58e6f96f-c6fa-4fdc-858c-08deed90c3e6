const mongoose = require('mongoose');
const Interest = require('../models/interest');
const User = require('../models/user');
const Question = require('../models/question');
const Comment = require('../models/comment');
const { addNumFollowersToInterest, saveCountsBasedOnCountryForInterest } = require('../lib/interest');
const { addNumQuestionsToInterest, calculateInterestPointsForInterest } = require('../lib/social');

const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/test';
const BATCH_SIZE = 5000;
let INTEREST_NAME = ''; // Set this to the interest want to undo the merge for

const addToArray = (arr, value) => {
  arr = arr ?? [];
  const valueStr = value.toString();

  if (!arr.some((item) => item.toString() === valueStr)) {
    arr.push(value);
  }
  return arr;
};

const removeFromArray = (arr, value) => (arr ?? []).filter((item) => item !== value);

async function processCollection(model, collectionName, updateConfig, oldInterest) {
  console.log(`\n=== Processing ${collectionName} ===`);
  let lastId = null;
  let hasMore = true;
  let totalProcessed = 0;
  const isQuestionOrComment = ['Questions', 'Comments'].includes(collectionName);

  const mergedInterest = oldInterest.mergedWith.name;

  while (hasMore) {
    const queryField = isQuestionOrComment ? 'previousInterestName' : 'previousAllInterestNames';

    const query = {
      [queryField]: oldInterest.name,
      ...(lastId ? { _id: { $lt: lastId } } : {}),
    };

    const docs = await model.find(query).sort({ _id: -1 }).limit(BATCH_SIZE).select(updateConfig.fields).lean();

    if (!docs.length) {
      hasMore = false;
      break;
    }

    const bulkOps = docs.map((doc) => {
      let updatedDoc = {};

      if (collectionName === 'Users') {
        // Preferences
        if (doc.previousInterestsData?.preferencesInterestNames?.includes(oldInterest.name)) {
          if (doc.previousInterestsData.preferencesInterestNames.includes(mergedInterest)) {
            doc.preferences.interestNames = addToArray(doc.preferences.interestNames, oldInterest.name);
            doc.preferences.interests = addToArray(doc.preferences.interests, oldInterest._id);
          } else {
            doc.preferences.interestNames = removeFromArray(doc.preferences.interestNames, mergedInterest);
            doc.preferences.interestNames = addToArray(doc.preferences.interestNames, oldInterest.name);

            doc.preferences.interests = removeFromArray(
              doc.preferences.interests.map((id) => id.toString()),
              oldInterest.mergedWith._id,
            ).map((id) => new mongoose.Types.ObjectId(id));
            doc.preferences.interests = addToArray(doc.preferences.interests, oldInterest._id);
          }
        }

        // Preferences Excluded Interest Names
        if (doc.previousInterestsData?.preferencesExcludedInterestNames?.includes(oldInterest.name)) {
          if (doc.previousInterestsData.preferencesExcludedInterestNames.includes(mergedInterest)) {
            doc.preferences.excludedInterestNames = addToArray(doc.preferences.excludedInterestNames, oldInterest.name);
          } else {
            doc.preferences.excludedInterestNames = removeFromArray(doc.preferences.excludedInterestNames, mergedInterest);
            doc.preferences.excludedInterestNames = addToArray(doc.preferences.excludedInterestNames, oldInterest.name);
          }
        }

        // Interests
        if (doc.previousInterestsData?.interestNames?.includes(oldInterest.name)) {
          if (doc.previousInterestsData.interestNames.includes(mergedInterest)) {
            doc.interestNames = addToArray(doc.interestNames, oldInterest.name);
            doc.interests = addToArray(doc.interests, oldInterest._id);
          } else {
            doc.interestNames = removeFromArray(doc.interestNames, mergedInterest);
            doc.interestNames = addToArray(doc.interestNames, oldInterest.name);

            doc.interests = removeFromArray(
              doc.interests.map((id) => id.toString()),
              oldInterest.mergedWith._id,
            ).map((id) => new mongoose.Types.ObjectId(id));
            doc.interests = addToArray(doc.interests, oldInterest._id);
          }
        }

        // Hidden Interests
        if (doc.previousInterestsData?.hiddenInterests?.includes(oldInterest.name)) {
          if (doc.previousInterestsData.hiddenInterests.includes(mergedInterest)) {
            doc.hiddenInterests = addToArray(doc.hiddenInterests, oldInterest.name);
          } else {
            doc.hiddenInterests = removeFromArray(doc.hiddenInterests, mergedInterest);
            doc.hiddenInterests = addToArray(doc.hiddenInterests, oldInterest.name);
          }
        }

        // Custom Feeds
        if (doc.previousInterestsData?.customFeedsInterestNames?.length) {
          doc.previousInterestsData.customFeedsInterestNames.forEach((feed) => {
            if (feed.interestNames.includes(oldInterest.name)) {
              const feedIndex = doc.customFeeds?.findIndex((f) => f.feedName === feed.feedName);
              if (feedIndex !== -1) {
                if (feed.interestNames.includes(mergedInterest)) {
                  doc.customFeeds[feedIndex].interestNames = addToArray(doc.customFeeds[feedIndex].interestNames, oldInterest.name);
                } else {
                  doc.customFeeds[feedIndex].interestNames = removeFromArray(doc.customFeeds[feedIndex].interestNames, mergedInterest);
                  doc.customFeeds[feedIndex].interestNames = addToArray(doc.customFeeds[feedIndex].interestNames, oldInterest.name);
                }
              }
            }
          });
        }

        // Clean previousAllInterestNames
        doc.previousAllInterestNames = removeFromArray(doc.previousAllInterestNames, oldInterest.name);

        // Prepare update operations
        const setFields = {
          preferences: doc.preferences,
          interests: doc.interests,
          interestNames: doc.interestNames,
          hiddenInterests: doc.hiddenInterests,
          customFeeds: doc.customFeeds,
        };
        const unsetFields = {};

        // Handle temporary fields - remove both if previousAllInterestNames becomes empty
        if (doc.previousAllInterestNames?.length === 0) {
          unsetFields.previousAllInterestNames = 1;
          unsetFields.previousInterestsData = 1;
        } else {
          setFields.previousAllInterestNames = doc.previousAllInterestNames;
        }

        updatedDoc = { setFields, unsetFields };
      }

      if (isQuestionOrComment) {
        updatedDoc = {
          setFields: { interestName: oldInterest.name },
          unsetFields: { previousInterestName: 1 },
        };
      }

      console.log(`Updating ${collectionName} ${doc._id}`);

      const updateOperation = {};
      if (updatedDoc.setFields && Object.keys(updatedDoc.setFields).length > 0) {
        updateOperation.$set = updatedDoc.setFields;
      }
      if (updatedDoc.unsetFields && Object.keys(updatedDoc.unsetFields).length > 0) {
        updateOperation.$unset = updatedDoc.unsetFields;
      }

      return {
        updateOne: {
          filter: { _id: doc._id },
          update: updateOperation,
        },
      };
    });

    if (bulkOps.length > 0) {
      await model.bulkWrite(bulkOps);
      totalProcessed += bulkOps.length;
      console.log(`Updated ${bulkOps.length} ${collectionName} docs, total so far: ${totalProcessed}`);
    }

    lastId = docs[docs.length - 1]._id;
  }
}

async function updateInterestRelatedStats(interest) {
  if (!interest) return;

  await addNumFollowersToInterest(interest);
  await addNumQuestionsToInterest(interest);
  await calculateInterestPointsForInterest(interest);
  await saveCountsBasedOnCountryForInterest(interest.name, interest.category);
  console.log(`Updated stats for interest: ${interest.name}`);
}

(async () => {
  try {
    await mongoose.connect(MONGODB_URI);
    console.log('Connected to MongoDB');

    const interest = await Interest.findOne({ name: INTEREST_NAME });
    if (!interest || !interest.mergedWith?.name) {
      console.log(`Interest "${INTEREST_NAME}" not found or not merged with any interest`);
      return;
    }

    console.log(`Starting Undo merge for: "${interest.name}" (mergedWith: "${interest.mergedWith.name}")`);

    const userConfig = {
      fields: '_id preferences customFeeds interests interestNames hiddenInterests previousAllInterestNames previousInterestsData',
    };

    const comment_question_Config = {
      fields: '_id interestName previousInterestName',
    };

    await processCollection(User, 'Users', userConfig, interest);
    await processCollection(Question, 'Questions', comment_question_Config, interest);
    await processCollection(Comment, 'Comments', comment_question_Config, interest);

    const mergedInterest = await Interest.findOne({ name: interest.mergedWith.name });
    for (const singleInterest of [interest, mergedInterest]) {
      await updateInterestRelatedStats(singleInterest);
    }

    interest.mergedWith = undefined;
    await interest.save();
    console.log(`Interest "${interest.name}" has been successfully restored from merge.`);
  } catch (error) {
    console.log('Error during undo merge:', error);
  } finally {
    await mongoose.disconnect();
    process.exit();
  }
})();
