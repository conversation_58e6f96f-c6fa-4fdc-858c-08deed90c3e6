const mongoose = require('mongoose');
const Interest = require('../models/interest');
const { loadInterestTranslations, translatedInterests } = require('../lib/interest');
const { startWatching } = require('../lib/interest-merge/new-interest-watcher');

const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/test';

(async () => {
  try {
    await mongoose.connect(MONGODB_URI);
    console.log('Connected to MongoDB');

    await loadInterestTranslations();
    await startWatching();

    // Create an interest document for testing
    const name = `testinterest${Math.floor(Math.random() * 1000)}`;
    const interest = new Interest({
      category: 'Test Category',
      name,
      interest: `#${name}`,
      translations: [
        {
          languageCode: 'en',
          name,
        },
        {
          languageCode: 'bn',
          name,
        },
      ],
    });
    await interest.save();
    console.log('Interest document created:', interest);

    // add a timeuot to allow the watcher to process the new interest, it takes some times
    await new Promise((resolve) => setTimeout(resolve, 3000));

    // Verify watcher added the interest to translatedInterests
    const interestId = interest._id.toString();
    const translatedInterest = translatedInterests[interestId];
    console.log('Translated interest:', translatedInterest);

    await new Promise((resolve) => setTimeout(resolve, 10000));
  } catch (error) {
    console.log('Error in interest watcher:', error);
  } finally {
    await mongoose.disconnect();
    console.log('Disconnected from MongoDB');
    process.exit();
  }
})();
