/*
ENV REQUIRED:
    - MONGODB_URI_RECORDS
    - MONGODB_URI
    - OPENAI_KEY
*/

const mongoose = require('mongoose');
const Interest = require('../models/interest');
const User = require('../models/user');
const Comment = require('../models/comment');
const Question = require('../models/question');
const InterestPoint = require('../models/interest-point');
const InterestCountryCount = require('../models/interest-country-count');
const { addNumFollowersToInterest, saveCountsBasedOnCountryForInterest } = require('../lib/interest');
const { addNumQuestionsToInterest, calculateInterestPointsForInterest } = require('../lib/social');
const { generateVector, translateInterest } = require('../lib/interest-merge/helper');
const { cleanInterestName } = require('../lib/interest');

const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/test';

const EXISTING_NAME = ''; // <-- Change to current interest name
const NEW_NAME = ''; // <-- Change to new desired name

async function buildTranslations(name) {
  const translations = (await translateInterest(name, [])) || {};

  return Object.entries(translations).map(([languageCode, rawName]) => ({
    languageCode,
    name: cleanInterestName(rawName),
  }));
}

async function updateUserCollection(oldInterestId, newInterestId) {
  const orConditions = [
    { interestNames: EXISTING_NAME },
    { hiddenInterests: EXISTING_NAME },
    { 'preferences.interestNames': EXISTING_NAME },
    { 'preferences.excludedInterestNames': EXISTING_NAME },
    { customFeeds: { $elemMatch: { interestNames: EXISTING_NAME } } },
    { interestPoints: { $elemMatch: { interest: EXISTING_NAME } } },
  ];

  if (!oldInterestId.equals(newInterestId)) {
    orConditions.push({ interests: oldInterestId }, { 'preferences.interests': oldInterestId });
  }

  const setUpdate = {
    interestNames: {
      $cond: {
        if: { $isArray: '$interestNames' },
        then: {
          $cond: {
            if: {
              $and: [{ $in: [EXISTING_NAME, '$interestNames'] }, { $not: { $in: [NEW_NAME, '$interestNames'] } }],
            },
            then: {
              $map: {
                input: '$interestNames',
                in: {
                  $cond: {
                    if: { $eq: ['$$this', EXISTING_NAME] },
                    then: NEW_NAME,
                    else: '$$this',
                  },
                },
              },
            },
            else: {
              $filter: {
                input: '$interestNames',
                cond: { $ne: ['$$this', EXISTING_NAME] },
              },
            },
          },
        },
        else: '$interestNames',
      },
    },
    hiddenInterests: {
      $cond: {
        if: { $isArray: '$hiddenInterests' },
        then: {
          $cond: {
            if: {
              $and: [{ $in: [EXISTING_NAME, '$hiddenInterests'] }, { $not: { $in: [NEW_NAME, '$hiddenInterests'] } }],
            },
            then: {
              $map: {
                input: '$hiddenInterests',
                in: {
                  $cond: {
                    if: { $eq: ['$$this', EXISTING_NAME] },
                    then: NEW_NAME,
                    else: '$$this',
                  },
                },
              },
            },
            else: {
              $filter: {
                input: '$hiddenInterests',
                cond: { $ne: ['$$this', EXISTING_NAME] },
              },
            },
          },
        },
        else: '$hiddenInterests',
      },
    },
    'preferences.interestNames': {
      $cond: {
        if: { $isArray: '$preferences.interestNames' },
        then: {
          $cond: {
            if: {
              $and: [{ $in: [EXISTING_NAME, '$preferences.interestNames'] }, { $not: { $in: [NEW_NAME, '$preferences.interestNames'] } }],
            },
            then: {
              $map: {
                input: '$preferences.interestNames',
                in: {
                  $cond: {
                    if: { $eq: ['$$this', EXISTING_NAME] },
                    then: NEW_NAME,
                    else: '$$this',
                  },
                },
              },
            },
            else: {
              $filter: {
                input: '$preferences.interestNames',
                cond: { $ne: ['$$this', EXISTING_NAME] },
              },
            },
          },
        },
        else: '$preferences.interestNames',
      },
    },
    'preferences.excludedInterestNames': {
      $cond: {
        if: { $isArray: '$preferences.excludedInterestNames' },
        then: {
          $cond: {
            if: {
              $and: [{ $in: [EXISTING_NAME, '$preferences.excludedInterestNames'] }, { $not: { $in: [NEW_NAME, '$preferences.excludedInterestNames'] } }],
            },
            then: {
              $map: {
                input: '$preferences.excludedInterestNames',
                in: {
                  $cond: {
                    if: { $eq: ['$$this', EXISTING_NAME] },
                    then: NEW_NAME,
                    else: '$$this',
                  },
                },
              },
            },
            else: {
              $filter: {
                input: '$preferences.excludedInterestNames',
                cond: { $ne: ['$$this', EXISTING_NAME] },
              },
            },
          },
        },
        else: '$preferences.excludedInterestNames',
      },
    },
    customFeeds: {
      $cond: {
        if: { $isArray: '$customFeeds' },
        then: {
          $map: {
            input: '$customFeeds',
            as: 'feed',
            in: {
              $mergeObjects: [
                '$$feed',
                {
                  interestNames: {
                    $cond: {
                      if: { $isArray: '$$feed.interestNames' },
                      then: {
                        $cond: {
                          if: {
                            $and: [{ $in: [EXISTING_NAME, '$$feed.interestNames'] }, { $not: { $in: [NEW_NAME, '$$feed.interestNames'] } }],
                          },
                          then: {
                            $map: {
                              input: '$$feed.interestNames',
                              in: {
                                $cond: {
                                  if: { $eq: ['$$this', EXISTING_NAME] },
                                  then: NEW_NAME,
                                  else: '$$this',
                                },
                              },
                            },
                          },
                          else: {
                            $filter: {
                              input: '$$feed.interestNames',
                              cond: { $ne: ['$$this', EXISTING_NAME] },
                            },
                          },
                        },
                      },
                      else: '$$feed.interestNames',
                    },
                  },
                },
              ],
            },
          },
        },
        else: '$customFeeds',
      },
    },
    interestPoints: {
      $cond: {
        if: { $isArray: '$interestPoints' },
        then: {
          $map: {
            input: '$interestPoints',
            as: 'point',
            in: {
              $cond: {
                if: { $eq: ['$$point.interest', EXISTING_NAME] },
                then: { $mergeObjects: ['$$point', { interest: NEW_NAME }] },
                else: '$$point',
              },
            },
          },
        },
        else: '$interestPoints',
      },
    },
  };

  if (!oldInterestId.equals(newInterestId)) {
    setUpdate.interests = {
      $cond: {
        if: { $isArray: '$interests' },
        then: {
          $cond: {
            if: {
              $and: [{ $in: [oldInterestId, '$interests'] }, { $not: { $in: [newInterestId, '$interests'] } }],
            },
            then: {
              $map: {
                input: '$interests',
                in: {
                  $cond: {
                    if: { $eq: ['$$this', oldInterestId] },
                    then: newInterestId,
                    else: '$$this',
                  },
                },
              },
            },
            else: {
              $filter: {
                input: '$interests',
                cond: { $ne: ['$$this', oldInterestId] },
              },
            },
          },
        },
        else: '$interests',
      },
    };

    setUpdate['preferences.interests'] = {
      $cond: {
        if: { $isArray: '$preferences.interests' },
        then: {
          $cond: {
            if: {
              $and: [{ $in: [oldInterestId, '$preferences.interests'] }, { $not: { $in: [newInterestId, '$preferences.interests'] } }],
            },
            then: {
              $map: {
                input: '$preferences.interests',
                in: {
                  $cond: {
                    if: { $eq: ['$$this', oldInterestId] },
                    then: newInterestId,
                    else: '$$this',
                  },
                },
              },
            },
            else: {
              $filter: {
                input: '$preferences.interests',
                cond: { $ne: ['$$this', oldInterestId] },
              },
            },
          },
        },
        else: '$preferences.interests',
      },
    };
  }

  const res = await User.updateMany({ $or: orConditions }, [{ $set: setUpdate }]);
  console.log(`Users updated: ${res.modifiedCount}`);
}

async function updateInterestNameInCollections(oldInterestId, newInterestId) {
  const updateIfNeeded = !oldInterestId.equals(newInterestId);

  const updates = [
    {
      model: Comment,
      filter: { interestName: EXISTING_NAME },
      update: { interestName: NEW_NAME },
      label: 'Comments',
    },
    {
      model: Question,
      filter: {
        $or: [{ interestName: EXISTING_NAME }, ...(updateIfNeeded ? [{ parent: oldInterestId }] : [])],
      },
      update: updateIfNeeded
        ? [
          {
            $set: {
              interestName: {
                $cond: [{ $eq: ['$interestName', EXISTING_NAME] }, NEW_NAME, '$interestName'],
              },
              parent: {
                $cond: [
                  {
                    $and: [{ $ne: ['$parent', null] }, { $eq: ['$parent', oldInterestId] }],
                  },
                  newInterestId,
                  '$parent',
                ],
              },
            },
          },
        ]
        : { interestName: NEW_NAME },
      label: 'Questions',
    },
  ];

  // Do not update InterestPoint and InterestCountryCount if changing to existing interest
  if (!updateIfNeeded) {
    updates.push({
      model: InterestPoint,
      filter: { interest: EXISTING_NAME },
      update: { interest: NEW_NAME },
      label: 'Interest Points',
    });

    updates.push({
      model: InterestCountryCount,
      filter: { interestName: EXISTING_NAME },
      update: { interestName: NEW_NAME },
      label: 'Interest Country Counts',
    });
  }

  for (const { model, filter, update, label } of updates) {
    const result = Array.isArray(update) ? await model.updateMany(filter, update) : await model.updateMany(filter, { $set: update });

    console.log(`${label} updated: ${result.modifiedCount}`);
  }
}

(async () => {
  try {
    if (!EXISTING_NAME || !NEW_NAME || EXISTING_NAME === NEW_NAME) {
      console.log('[Error] Please provide valid and different EXISTING_NAME and NEW_NAME values.');
      process.exit(1);
    }

    await mongoose.connect(MONGODB_URI);
    console.log('Connected to MongoDB');

    const interests = await Interest.find({ name: { $in: [EXISTING_NAME, NEW_NAME] } });
    const existingInterest = interests.find(i => i.name === EXISTING_NAME);
    const duplicateInterest = interests.find(i => i.name === NEW_NAME);

    if (!existingInterest) {
      console.log(`[Error] Interest "${EXISTING_NAME}" does not exist.`);
      process.exit(1);
    }

    if (duplicateInterest) {
      // Prepare duplicate as primary interest
      if (!duplicateInterest.embedding) {
        duplicateInterest.embedding = await generateVector(duplicateInterest.name);
      }
      if (!duplicateInterest.translations?.length) {
        duplicateInterest.translations = await buildTranslations(duplicateInterest.name);
      }
      duplicateInterest.status = null;
      duplicateInterest.mergedWith = undefined;
      await duplicateInterest.save();

      // Reject original
      existingInterest.status = 'rejected';
      existingInterest.embedding = undefined;
      existingInterest.translations = undefined;
    } else {
      // Rename original
      existingInterest.name = NEW_NAME;
      existingInterest.interest = `#${NEW_NAME}`;
      existingInterest.embedding = await generateVector(NEW_NAME);
      duplicateInterest.translations = await buildTranslations(NEW_NAME);
    }
    await existingInterest.save();

    const newInterestId = duplicateInterest ? duplicateInterest._id : existingInterest._id;
    await Interest.updateMany(
      { 'mergedWith.name': EXISTING_NAME },
      {
        $set: {
          mergedWith: {
            _id: newInterestId.toString(),
            name: NEW_NAME,
          },
        },
      },
    );

    // Update user and related collections
    await updateUserCollection(existingInterest._id, newInterestId);
    await updateInterestNameInCollections(existingInterest._id, newInterestId);

    // Recalculate stats
    if (duplicateInterest) {
      await addNumFollowersToInterest(duplicateInterest);
      await addNumQuestionsToInterest(duplicateInterest);
      await calculateInterestPointsForInterest(duplicateInterest);
      await saveCountsBasedOnCountryForInterest(duplicateInterest.name, duplicateInterest.category);
      console.log(`Updated stats for interest: ${duplicateInterest.name}`);
    }

    console.log(`Successfully renamed "${EXISTING_NAME}" to "${NEW_NAME}" across all collections.`);
  } catch (error) {
    console.log('Error during rename operation:', error.message || error);
  } finally {
    await mongoose.disconnect();
    process.exit();
  }
})();
