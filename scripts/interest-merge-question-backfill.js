const mongoose = require('mongoose');
const Interest = require('../models/interest');
const Question = require('../models/question');

const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/test';
const BATCH_SIZE = 10000;
const oldIdtoNewIdMap = {};
const oldNameToNewNameMap = {};

const buildBulkOpsForQuestions = (questions) =>
  questions.map((question) => {
    const isQuestion = question.interestName === 'questions';
    const updatedQuestion = {
      parent: isQuestion
        ? question.parent
        : oldIdtoNewIdMap[question.parent?.toString()]
          ? new mongoose.Types.ObjectId(oldIdtoNewIdMap[question.parent?.toString()])
          : question.parent,
      interestName: isQuestion ? question.interestName : oldNameToNewNameMap[question.interestName] || question.interestName,
      interestMergeChecked: true,
    };

    if (question.interestName !== updatedQuestion.interestName) {
      updatedQuestion.previousInterestName = question.interestName;
    }

    console.log(
      `Updating question ${question._id} with interestName: ${updatedQuestion.interestName}, previous interestName: ${question.interestName}, parent: ${updatedQuestion.parent}`,
    );

    return {
      updateOne: {
        filter: { _id: question._id },
        update: { $set: updatedQuestion },
      },
    };
  });

(async () => {
  try {
    await mongoose.connect(MONGODB_URI);
    console.log('Connected to MongoDB');

    const mergedInterests = await Interest.find({ mergedWith: { $exists: true } })
      .select('_id name mergedWith')
      .lean();

    mergedInterests.forEach((interest) => {
      oldIdtoNewIdMap[interest._id.toString()] = interest.mergedWith._id;
      oldNameToNewNameMap[interest.name] = interest.mergedWith.name;
    });

    let hasMore = true;
    let totalProcessed = 0;

    const lastProcessed = await Question.findOne({ interestMergeChecked: true })
      .sort({ _id: 1 })
      .select('_id')
      .lean();
    let lastId = lastProcessed ? lastProcessed._id : null;

    while (hasMore) {
      try {
        console.log('Last processed question ID:', lastId);

        const query = lastId ? { _id: { $lt: lastId } } : {};
        const questions = await Question.find(query)
          .sort({ _id: -1 })
          .limit(BATCH_SIZE)
          .select('_id parent interestName')
          .lean();

        if (!questions.length) {
          hasMore = false;
          break;
        }

        const bulkOps = buildBulkOpsForQuestions(questions);
        const result = await Question.bulkWrite(bulkOps);
        lastId = questions[questions.length - 1]._id?.toString();
        totalProcessed += questions.length;
        console.log(`Batch stats: matched=${result.matchedCount}, modified=${result.modifiedCount}`);
        console.log(`Total Processed ${totalProcessed} questions`);

        await new Promise((res) => setTimeout(res, 100)); // Delay to avoid overwhelming the database
      } catch (error) {
        console.log(`Failed bulkWrite at _id < ${lastId}:`, error);
      }
    }
  } catch (error) {
    console.log('Error updating questions interests:', error);
  } finally {
    await mongoose.disconnect();
    console.log('Disconnected from MongoDB');
    process.exit();
  }
})();
