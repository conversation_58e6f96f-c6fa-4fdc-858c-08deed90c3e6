const mongoose = require('mongoose');
const Interest = require('../models/interest');
const Comment = require('../models/comment');

const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/test';
const BATCH_SIZE = 10000;
const oldIdtoNewIdMap = {};
const oldNameToNewNameMap = {};

const buildBulkOpsForComments = (comments) =>
  comments.map((comment) => {
    const updatedComment = {
      interestName: comment.interestName ? oldNameToNewNameMap[comment.interestName] || comment.interestName : comment.interestName,
      interestMergeChecked: true,
    };

    if (comment.interestName !== updatedComment.interestName) {
      updatedComment.previousInterestName = comment.interestName;
    }

    console.log(
      `Updating comment ${comment._id} with interestName: ${updatedComment.interestName}, previous interestName: ${comment.interestName}`,
    );

    return {
      updateOne: {
        filter: { _id: comment._id },
        update: { $set: updatedComment },
      },
    };
  });

(async () => {
  try {
    await mongoose.connect(MONGODB_URI);
    console.log('Connected to MongoDB');

    const mergedInterests = await Interest.find({ mergedWith: { $exists: true } })
      .select('_id name mergedWith')
      .lean();

    mergedInterests.forEach((interest) => {
      oldIdtoNewIdMap[interest._id.toString()] = interest.mergedWith._id;
      oldNameToNewNameMap[interest.name] = interest.mergedWith.name;
    });

    let hasMore = true;
    let totalProcessed = 0;

    const lastProcessed = await Comment.findOne({ interestMergeChecked: true })
      .sort({ _id: 1 })
      .select('_id')
      .lean();
    let lastId = lastProcessed ? lastProcessed._id : null;

    while (hasMore) {
      try {
        console.log('Last processed question ID:', lastId);

        const query = lastId ? { _id: { $lt: lastId } } : {};
        const comments = await Comment.find(query)
          .sort({ _id: -1 })
          .limit(BATCH_SIZE)
          .select('_id interestName')
          .lean();

        if (!comments.length) {
          hasMore = false;
          break;
        }

        const bulkOps = buildBulkOpsForComments(comments);
        const result = await Comment.bulkWrite(bulkOps);
        lastId = comments[comments.length - 1]._id;
        totalProcessed += comments.length;
        console.log(`Batch stats: matched=${result.matchedCount}, modified=${result.modifiedCount}`);
        console.log(`Total Processed ${totalProcessed} comments`);

        await new Promise((res) => setTimeout(res, 100)); // Delay to avoid overwhelming the database
      } catch (error) {
        console.log(`Failed bulkWrite at _id < ${lastId}:`, error);
      }
    }
  } catch (error) {
    console.log('Error updating comments interests:', error);
  } finally {
    await mongoose.disconnect();
    console.log('Disconnected from MongoDB');
    process.exit();
  }
})();
