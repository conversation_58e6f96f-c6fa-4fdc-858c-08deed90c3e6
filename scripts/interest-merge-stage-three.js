const fs = require('fs').promises;
const path = require('path');
const mongoose = require('mongoose');
const Interest = require('../models/interest');
// eslint-disable-next-line import/no-unresolved
const { stringify } = require('csv-stringify/sync');

const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/test';
const BATCH_SIZE = 5000;
let TOTAL_PROCESSED = 0;

(async () => {
  try {
    await mongoose.connect(MONGODB_URI);
    console.log('Connected to MongoDB');

    let hasMoreData = true;
    let lastProcessedId = null;
    let allData = [];

    while (hasMoreData) {
      const query = {
        translations: { $exists: true },
      };

      if (lastProcessedId) {
        query._id = { $gt: lastProcessedId };
      }

      const batch = await Interest.find(query).select('_id category name translations').sort({ _id: 1 }).limit(BATCH_SIZE).lean();

      if (batch.length === 0) {
        hasMoreData = false;
        break;
      }

      batch.forEach((item) => {
        const translations =
          item.translations?.reduce((acc, { languageCode, name }) => {
            acc[languageCode] = name;
            return acc;
          }, {}) || {};

        allData.push([item._id.toString(), item.category, item.name, JSON.stringify(translations)]);
      });

      lastProcessedId = batch[batch.length - 1]._id;
      TOTAL_PROCESSED += batch.length;

      console.log(`Processed batch: ${batch.length}, Total so far: ${TOTAL_PROCESSED}`);
    }

    const csvData = stringify(allData, {
      header: true,
      columns: {
        0: '_id',
        1: 'category',
        2: 'name',
        3: 'translations',
      },
    });

    const filePath = path.join(__dirname, '../lib/interest-merge/interest-exports.csv');
    await fs.writeFile(filePath, csvData);

    console.log(`Total ${TOTAL_PROCESSED} interest records exported to ${filePath}`);
  } catch (error) {
    console.log('Error exporting interests:', error);
  } finally {
    console.log('Complete exporting interests');
    await mongoose.disconnect();
    console.log('Disconnected from MongoDB');
    process.exit();
  }
})();
