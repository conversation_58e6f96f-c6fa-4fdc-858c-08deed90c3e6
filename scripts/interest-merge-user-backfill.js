const mongoose = require('mongoose');
const Interest = require('../models/interest');
const User = require('../models/user');

const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/test';
const BATCH_SIZE = 10000;
const oldIdtoNewIdMap = {};
const oldNameToNewNameMap = {};

const getUpdatedIds = (ids) => {
  const updatedIds = new Set();
  ids.forEach((id) => {
    const oldId = id.toString();
    updatedIds.add(oldIdtoNewIdMap[oldId] || oldId);
  });
  return Array.from(updatedIds.map((id) => new mongoose.Types.ObjectId(id)));
};

const getUpdatedNames = (names) => {
  const updatedNames = new Set();
  names.forEach((name) => {
    updatedNames.add(oldNameToNewNameMap[name] || name);
  });
  return Array.from(updatedNames);
};

const buildBulkOpsForUsers = (users) =>
  users.map((user) => {
    const previousInterestNamesSet = new Set();
    const previousInterestsData = {};
    const updatedUser = {
      interestMergeChecked: true,
    };

    if (user.preferences?.interestNames?.length > 0 || user.preferences?.excludedInterestNames?.length > 0) {
      const { interests, interestNames, excludedInterestNames } = user.preferences;

      const updatedPrefInterests = interests ? getUpdatedIds(interests) : undefined;
      const updatedPrefInterestNames = interestNames ? getUpdatedNames(interestNames) : undefined;
      const updatedExcludedInterestNames = excludedInterestNames ? getUpdatedNames(excludedInterestNames) : undefined;

      updatedUser.preferences = {
        ...user.preferences,
        ...(updatedPrefInterests && { interests: updatedPrefInterests }),
        ...(updatedPrefInterestNames && { interestNames: updatedPrefInterestNames }),
        ...(updatedExcludedInterestNames && { excludedInterestNames: updatedExcludedInterestNames }),
      };

      [
        { key: 'preferencesInterestNames', old: interestNames, updated: updatedPrefInterestNames },
        { key: 'preferencesExcludedInterestNames', old: excludedInterestNames, updated: updatedExcludedInterestNames },
      ].forEach(({ key, old, updated }) => {
        if (old && updated) {
          const hasDiff = old.some((name) => !updated.includes(name));
          if (hasDiff) {
            previousInterestsData[key] = old;
            old.forEach((name) => previousInterestNamesSet.add(name));
          }
        }
      });
    }

    if (user.customFeeds?.length > 0) {
      updatedUser.customFeeds = user.customFeeds.map((feed) => {
        const updatedFeed = { ...feed };

        if (feed.interestNames?.length > 0) {
          const updatedFeedInterestNames = getUpdatedNames(feed.interestNames);

          updatedFeed.interestNames = updatedFeedInterestNames;

          if (feed.interestNames.some((name) => !updatedFeedInterestNames.includes(name))) {
            if (!previousInterestsData.customFeedsInterestNames) {
              previousInterestsData.customFeedsInterestNames = [];
            }

            previousInterestsData.customFeedsInterestNames.push({
              feedName: feed.feedName,
              interestNames: feed.interestNames,
            });

            feed.interestNames.forEach((name) => previousInterestNamesSet.add(name));
          }
        }

        return updatedFeed;
      });
    }

    if (user.interests?.length > 0) {
      updatedUser.interests = getUpdatedIds(user.interests);
    }

    if (user.interestNames?.length > 0) {
      const updatedInterestNames = getUpdatedNames(user.interestNames);

      updatedUser.interestNames = updatedInterestNames;

      if (user.interestNames.some((name) => !updatedInterestNames.includes(name))) {
        previousInterestsData.interestNames = user.interestNames;
        user.interestNames.forEach((name) => previousInterestNamesSet.add(name));
      }
    }

    if (user.hiddenInterests?.length > 0) {
      const updatedHiddenInterests = getUpdatedNames(user.hiddenInterests);

      updatedUser.hiddenInterests = updatedHiddenInterests;

      if (user.hiddenInterests.some((name) => !updatedHiddenInterests.includes(name))) {
        previousInterestsData.hiddenInterests = user.hiddenInterests;
        user.hiddenInterests.forEach((name) => previousInterestNamesSet.add(name));
      }
    }

    if (previousInterestNamesSet.size > 0) {
      updatedUser.previousAllInterestNames = Array.from(previousInterestNamesSet);
    }

    if (Object.keys(previousInterestsData).length > 0) {
      updatedUser.previousInterestsData = previousInterestsData;
    }

    console.log(`Updating user ${user._id} with interests: ${JSON.stringify(updatedUser.interestNames)}, previous: ${JSON.stringify(user.interestNames)}`);

    return {
      updateOne: {
        filter: { _id: user._id },
        update: { $set: updatedUser },
      },
    };
  });

(async () => {
  try {
    await mongoose.connect(MONGODB_URI);
    console.log('Connected to MongoDB');

    const mergedInterests = await Interest.find({ mergedWith: { $exists: true } })
      .select('_id name mergedWith')
      .lean();

    mergedInterests.forEach((interest) => {
      oldIdtoNewIdMap[interest._id.toString()] = interest.mergedWith._id;
      oldNameToNewNameMap[interest.name] = interest.mergedWith.name;
    });

    let hasMore = true;
    let totalProcessed = 0;

    const lastProcessed = await User.findOne({ interestMergeChecked: true })
      .sort({ _id: 1 })
      .select('_id')
      .lean();
    let lastId = lastProcessed ? lastProcessed._id : null;

    while (hasMore) {
      try {
        console.log('Last processed user ID:', lastId);

        const query = lastId ? { _id: { $lt: lastId } } : {};
        const users = await User.find(query)
          .sort({ _id: -1 })
          .limit(BATCH_SIZE)
          .select('_id preferences customFeeds interests interestNames hiddenInterests interestPoints')
          .lean();

        if (!users.length) {
          hasMore = false;
          break;
        }

        const bulkOps = buildBulkOpsForUsers(users);
        const result = await User.bulkWrite(bulkOps);
        lastId = users[users.length - 1]._id;
        totalProcessed += users.length;
        console.log(`Batch stats: matched=${result.matchedCount}, modified=${result.modifiedCount}`);
        console.log(`Total Processed ${totalProcessed} users`);

        await new Promise((res) => setTimeout(res, 100)); // Delay to avoid overwhelming the database
      } catch (error) {
        console.log(`Failed bulkWrite at _id < ${lastId}:`, error);
      }
    }
  } catch (error) {
    console.log('Error updating user interests:', error);
  } finally {
    await mongoose.disconnect();
    console.log('Disconnected from MongoDB');
    process.exit();
  }
})();
