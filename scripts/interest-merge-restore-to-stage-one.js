/* eslint-disable no-continue */
/*
Required Files:
lib/interest-merge/final_interest_group.json (Added in the PR)

Required ENV: MONGODB_URI, MONGODB_URI_RECORDS, OPENAI_KEY
*/

const fs = require('fs').promises;
const path = require('path');
const mongoose = require('mongoose');
// eslint-disable-next-line import/no-unresolved
const { parse } = require('csv-parse/sync');
const Interest = require('../models/interest');

const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/test';

(async () => {
  try {
    await mongoose.connect(MONGODB_URI);
    console.log('[INFO] Connected to MongoDB');

    // Load grouped interests
    const groupFilePath = path.join(__dirname, '..', 'lib', 'interest-merge', 'final_interest_group.json');

    let interestGroupData;
    try {
      const fileContents = await fs.readFile(groupFilePath, 'utf8');
      interestGroupData = JSON.parse(fileContents);
    } catch (err) {
      console.log('[ERROR] Failed to read or parse interest group JSON:', err);
      process.exit(1);
    }

    const interests = new Set();

    for (const line of interestGroupData) {
      if (line.Interest) interests.add(line.Interest);

      if (line.SimilarInterests) {
        line.SimilarInterests.split(',')
          .map((i) => i.trim())
          .forEach((interest) => interests.add(interest));
      }
    }

    // Load onboarding interests
    const onboardingCsvPath = path.join(__dirname, '..', 'lib', 'onboarding_interests.csv');
    let onboardingInterestsData;
    try {
      const onboardingCsv = await fs.readFile(onboardingCsvPath, 'utf8');
      onboardingInterestsData = parse(onboardingCsv, {
        delimiter: ',',
        columns: true,
        skip_empty_lines: true,
      });
    } catch (err) {
      console.log('[ERROR] Failed to read or parse onboarding CSV:', err);
      process.exit(1);
    }

    onboardingInterestsData.forEach((item) => interests.add(item.name));
    console.log('[INFO] Found', interests.size, 'unique interests');

    await Interest.updateMany(
      { name: { $nin: Array.from(interests) } },
      {
        $unset: {
          processedDuringMerge: '',
          translations: '',
          embedding: '',
          mergedWith: '',
        },
      },
    );
  } catch (error) {
    console.log('[ERROR] Error during interest merge:', error);
    process.exit(1);
  } finally {
    console.log('[INFO] Completed processing interest groups');
    await mongoose.disconnect();
    console.log('[INFO] Disconnected from MongoDB');
    process.exit(0);
  }
})();
