const mongoose = require('mongoose');
const Interest = require('../models/interest');

const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/test';
const BATCH_SIZE = 5000;

(async () => {
  let totalDeleted = 0;
  try {
    await mongoose.connect(MONGODB_URI);
    console.log('Connected to MongoDB');

    let hasMore = true;
    let lastId = null;

    while (hasMore) {
      try {
        const query = {
          $or: [
            { mergedWith: { $exists: true } }, // can remove this if we want to keep merged interests
            { numFollowers: 0, numQuestions: 0 },
          ],
          ...(lastId ? { _id: { $gt: lastId } } : {}),
        };

        const interests = await Interest.find(query).sort({ _id: 1 }).limit(BATCH_SIZE).select('_id name numFollowers numQuestions mergedWith').lean();

        if (!interests.length) {
          hasMore = false;
          break;
        }

        const bulkOps = interests.map((i) => {
          console.log(`Deleting interest ${i._id} name ${i.name} numFollowers ${i.numFollowers} numQuestions ${i.numQuestions} mergedWith ${i.mergedWith}`);
          return {
            deleteOne: { filter: { _id: i._id } },
          };
        });

        const result = await Interest.bulkWrite(bulkOps);
        lastId = interests[interests.length - 1]._id;
        totalDeleted += result.deletedCount;

        console.log(`Deleted ${result.deletedCount} documents (total: ${totalDeleted})`);

        await new Promise((res) => setTimeout(res, 100));
      } catch (batchError) {
        console.log(' Error during batch delete:', batchError);
      }
    }
  } catch (error) {
    console.log('Connection or setup error:', error);
  } finally {
    await mongoose.disconnect();
    console.log(`Disconnected from MongoDB. Total deleted: ${totalDeleted}`);
    process.exit();
  }
})();
