/*
ENV REQUIRED:
  - PROD_DB_URI
  - BETA_DB_URI
*/

const mongoose = require('mongoose');
const { languageCodes } = require('../lib/languages');

const PROD_DB_URI = process.env.PROD_DB_URI;
const BETA_DB_URI = process.env.BETA_DB_URI;
const BATCH_SIZE = 20000;

const interestSchema = new mongoose.Schema({
  createdAt: { type: Date, default: Date.now },
  createdBy: { type: String, ref: 'User' },
  category: String,
  libCategory: String,
  interest: { type: String, unique: true, trim: true },
  name: { type: String, unique: true, trim: true },
  sortIndex: Number,
  allowImages: { type: Boolean, default: true },
  pending: Boolean,
  status: { type: String, enum: ['pending', 'rejected', null] },
  openaiApproval: Boolean,
  numFollowers: { type: Number, default: 0 },
  numFollowersSortIndex: { type: Number, default: Math.random },
  numQuestions: { type: Number, default: 0 },
  numQuestionsPerLanguage: {
    type: Map,
    of: Number,
    default: {},
  },
  eligibleForRanks: {
    type: Map,
    of: Boolean,
    default: {},
  },
  similar: [String],
  language: { type: String, enum: languageCodes },
  lastPostAddedTime: Date,
  translations: {
    type: [
      {
        _id: false,
        languageCode: String,
        name: { type: String, trim: true },
      },
    ],
    default: undefined,
  },
  embedding: { type: [Number], default: undefined },
  processedDuringMerge: Boolean,
  mergedWith: {
    _id: String,
    name: String,
  },
});

(async () => {
  let prodConnection;
  let betaConnection;

  try {
    prodConnection = await mongoose.createConnection(PROD_DB_URI).asPromise();
    betaConnection = await mongoose.createConnection(BETA_DB_URI).asPromise();
    console.log('Connected to both databases');

    const ProdInterest = prodConnection.model('Interest', interestSchema);
    const BetaInterest = betaConnection.model('Interest', interestSchema);

    let count = 0;
    let lastId = null;
    let inserted = 0;
    let skipped = 0;
    let errors = 0;
    let hasMore = true;

    while (hasMore) {
      const query = lastId ? { _id: { $gt: lastId } } : {};
      const interests = await ProdInterest.find(query).sort({ _id: 1 }).limit(BATCH_SIZE).lean();

      if (interests.length === 0) {
        hasMore = false;
        break;
      }

      const names = interests.map((i) => i.name);
      const existing = await BetaInterest.find({ name: { $in: names } })
        .select('name')
        .lean();
      const existingNames = new Set(existing.map((e) => e.name));

      const newInterests = interests.filter((i) => !existingNames.has(i.name));

      if (newInterests.length > 0) {
        try {
          const bulkOps = newInterests.map((doc) => {
            const { createdBy, ...rest } = doc;
            return {
              insertOne: {
                document: rest,
              },
            };
          });

          const result = await BetaInterest.bulkWrite(bulkOps);
          inserted += result.insertedCount || 0;
        } catch (err) {
          console.log('Bulk write error:', err);
          errors += newInterests.length;
        }
      }

      skipped += interests.length - newInterests.length;
      count += interests.length;
      lastId = interests[interests.length - 1]._id;

      console.log(`Processed: ${count}, Inserted: ${inserted}, Skipped: ${skipped}, Errors: ${errors}`);
    }
  } catch (err) {
    console.log('Error during interest export:', err);
    process.exitCode = 1;
  } finally {
    await Promise.all([
      prodConnection?.close().catch((e) => console.log('Error closing prodConnection:', e)),
      betaConnection?.close().catch((e) => console.log('Error closing betaConnection:', e)),
    ]);
    console.log('Connections closed.');
    process.exit();
  }
})();
