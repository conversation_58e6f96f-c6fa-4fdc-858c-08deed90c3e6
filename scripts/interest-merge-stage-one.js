/* eslint-disable no-continue */
/*
Required Files:
lib/interest-merge/final_interest_group.json (Added in the PR)

Required ENV: MONGODB_URI, MONGODB_URI_RECORDS, OPENAI_KEY
*/

const fs = require('fs').promises;
const mongoose = require('mongoose');
// eslint-disable-next-line import/no-unresolved
const { parse } = require('csv-parse/sync');
const Interest = require('../models/interest');
const { translateInterest, mergeInterests, generateVector } = require('../lib/interest-merge/helper');
const { cleanInterestName, i18n_interests } = require('../lib/interest');
const { locales } = require('../lib/translate');

const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/test';

// Function to determine the main interest
const getMainInterest = async (interests, cleanedObj) => {
  let mainInterest;
  if (interests[0].name === cleanedObj.en) {
    mainInterest = interests[0]; // Use the first interest as the main interest, if it's already in english
  } else {
    mainInterest = await Interest.findOne({ name: cleanedObj.en }); // Check if the english translation is already present in the database
    if (!mainInterest) {
      // Create a new main interest if it does not exist
      mainInterest = new Interest({
        name: cleanedObj.en,
        interest: `#${cleanedObj.en}`,
        status: null,
      });
      console.log(`Creating new interest ${mainInterest.name} from ${interests[0].name}`);
    }
  }
  return mainInterest;
};

const BATCH_SIZE = 50;
const FAILED_GROUPS_FILE = `${__dirname}/../lib/interest-merge/merge_failed_groups.json`;
let FAILED_GROUPS = [],
  PROCESSED = [];

const processOnboardingInterests = async (interest) => {
  console.log('Processing onboarding interest:', interest);
  try {
    const translations = [];
    for (const locale of locales) {
      if (locale) {
        translations.push({
          languageCode: locale,
          name: i18n_interests.__({ phrase: interest, locale }),
        });
      }
    }
    const embedding = await generateVector(interest);
    await Interest.updateOne({ name: interest }, { $set: { translations, processedDuringMerge: true, embedding: embedding || undefined } });
  } catch (error) {
    console.log('Error processing onboarding interest:', error.message);
    FAILED_GROUPS.push({ Interest: interest, onBoarding: true });
  }
};

const processInterestGroup = async (group, noTimeout) => {
  console.log('Processing interest group:', group.Interest);
  const interestsSet = new Set([group.Interest, ...group.SimilarInterests.split(',').map((i) => i.trim())]);
  if (interestsSet.size <= 1) return;

  let interests,
    cleanedObj = {},
    mainInterest;

  try {
    if (group.onBoarding) {
      // Onboarding: prioritize the main interest
      interests = await Interest.find({ name: { $in: Array.from(interestsSet) }, processedDuringMerge: { $exists: false }, status: null });

      if (interests.length === 0) throw new Error(`No interests found: ${Array.from(interestsSet).join(', ')}`);

      for (const locale of locales) {
        if (locale) {
          cleanedObj[locale] = i18n_interests.__({ phrase: group.Interest, locale });
        }
      }
      mainInterest = interests.find((interest) => interest.name === group.Interest);
      PROCESSED.push(group.Interest);
    } else {
      // fetch and sort interests by number of questions
      interests = await Interest.find({
        name: { $in: Array.from(interestsSet) },
        processedDuringMerge: { $exists: false },
        status: null,
      }).sort({ numQuestions: -1 });

      if (interests.length === 0) throw new Error(`No interests found: ${Array.from(interestsSet).join(', ')}`);

      const response = await translateInterest(
        interests[0].name,
        interests.slice(1).map((i) => i.name),
        noTimeout,
      );

      if (response) {
        cleanedObj = Object.fromEntries(Object.keys(response).map((key) => [key, cleanInterestName(response[key])]));
        mainInterest = await getMainInterest(interests, cleanedObj);
      }
    }

    if (!mainInterest) throw new Error(`Unable to determine main interest for group: ${group.Interest}`);

    const mergedInterests = [];
    // Merge number of followers and questions
    for (const similarInterest of interests) {
      if (similarInterest.name === mainInterest.name) continue;
      mergeInterests(mainInterest, similarInterest);
      mergedInterests.push(similarInterest.name);
    }

    // Update main interest and save it
    mainInterest.processedDuringMerge = true;
    mainInterest.translations = Object.keys(cleanedObj).map((key) => ({
      languageCode: key,
      name: cleanedObj[key],
    }));
    // generate vector embedding for interest name only
    const embedding = await generateVector(mainInterest.name);
    if (embedding) mainInterest.embedding = embedding;
    await mainInterest.save();

    // Mark all merged interests as processed and point them to the main interest
    await Interest.updateMany(
      { name: { $in: mergedInterests } },
      {
        $set: {
          processedDuringMerge: true,
          mergedWith: {
            _id: mainInterest._id.toString(),
            name: mainInterest.name,
          },
          translations: undefined,
          embedding: undefined,
        },
      },
    );

    // Will re-calculate after the merge
    // Update InterestCountryCount for merged interests
    // await mergeInterestCountryCounts(mainInterest.name, mergedInterests);

    // Update InterestPoint for main interest
    // for (const mergedInterestName of mergedInterests) {
    //   await InterestPoint.updateMany({ interest: mergedInterestName }, { $set: { interest: mainInterest.name } });
    // }

    // Remove the group from failed groups if previously added
    FAILED_GROUPS = FAILED_GROUPS.filter((item) => item.Interest !== group.Interest);
  } catch (error) {
    console.log('Error processing interest group:', error.message);
    // Avoid insert into failed groups from the retry
    if (!noTimeout) {
      FAILED_GROUPS.push(group);
    }
  }
};

// This function processes interest groups and onboarding interests
(async () => {
  try {
    await mongoose.connect(MONGODB_URI);
    console.log('Connected to MongoDB');

    // Loading grouped interests
    const file = `${__dirname}/../lib/interest-merge/final_interest_group.json`;
    const data = await fs.readFile(file, 'utf8');
    const lines = JSON.parse(data);

    // Load failed groups
    try {
      const failedGroupsData = await fs.readFile(FAILED_GROUPS_FILE, 'utf8');
      FAILED_GROUPS = JSON.parse(failedGroupsData);
    } catch (err) {
      console.log('No existing failed groups.');
    }

    // Load onboarding interests
    const onboardingInterestsData = parse(await fs.readFile(`${__dirname}/../lib/onboarding_interests.csv`, 'utf8'), {
      delimiter: ',',
      columns: true,
      skip_empty_lines: true,
    });
    let onboardingInterests = onboardingInterestsData.map((item) => item.name);

    const batches = [];
    for (let i = 0; i < lines.length; i += BATCH_SIZE) {
      batches.push(lines.slice(i, i + BATCH_SIZE));
    }

    for (let i = 0; i < batches.length; i++) {
      await Promise.all(batches[i].map(processInterestGroup));
      console.log(`Processed batch ${i + 1} of ${batches.length}`);
    }

    // Process remaining onboarding interests
    onboardingInterests = onboardingInterests.filter((item) => !PROCESSED.includes(item));
    for (let i = 0; i < onboardingInterests.length; i += 500) {
      const batch = onboardingInterests.slice(i, i + 500);
      await Promise.all(batch.map(processOnboardingInterests));
    }

    // Process failed groups, one item at a time without enforced timeout
    for (const item of FAILED_GROUPS) {
      await processInterestGroup(item, true);
      console.log(`Processed failed group: ${item.Interest}`);
    }

    if (FAILED_GROUPS.length > 0) {
      await fs.writeFile(FAILED_GROUPS_FILE, JSON.stringify(FAILED_GROUPS, null, 2));
      console.log('Failed groups written to file:', FAILED_GROUPS_FILE);
    } else {
      await fs.unlink(FAILED_GROUPS_FILE).catch(() => {});
    }
  } catch (error) {
    console.log('Error during interest merge:', error);
  } finally {
    console.log('Completed processing interest groups');
    await mongoose.disconnect();
    console.log('Disconnected from MongoDB');
  }
})();
