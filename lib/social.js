const mongoose = require('mongoose');
const moment = require('moment');
const cmp = require('semver-compare');
const { DateTime } = require('luxon');
const _ = require('underscore');
const User = require('../models/user');
const UserMetadata = require('../models/user-metadata');
const Interest = require('../models/interest');
const Question = require('../models/question');
const Comment = require('../models/comment');
const Notification = require('../models/notification');
const Follow = require('../models/follow');
const Award = require('../models/award');
const Chat = require('../models/chat');
const Message = require('../models/message');
const WebPage = require('../models/web-page');
const Block = require('../models/block');
const locationLib = require('../lib/location');
const actionLib = require('./action');
const { translate, locales } = require('./translate');
const { getBlockLookup } = require('./projections');
const {
  notFoundError, forbiddenError, badRequestError, conflictError, applicationError, invalidInputError,
} = require('./http-errors');
const {
  getKarmaTiers, getNewKarmaCoinRewards, getNewKarmaTiers, WEB_DOMAIN,
} = require('./constants');
const constants = require('./constants');
const coinsLib = require('./coins');
const { formatProfile } = require('./chat');
const interestLib = require('./interest');
const webIdLib = require('./web-id');
const { languageCodes } = require('./languages');
const admin = require('../config/firebase-admin');
const coinsConstants = require('./coins-constants');
const abTest = require('./ab-test');
const { isLocal } = require('./location');
const socketLib = require('./socket');
const { arrayDiff } = require('./basic');
const { contiguous_keywords, extractValidWords } = require('./text-search');
const { hasDuplicates } = require('./basic');
const { isTextInappropriate } = require('./text-moderation');
const {
  onGiveLikes, onGetLikes, onGiveAwards, onGetAwards,
} = require('./coins');
const FriendList = require('../models/friend-list');
const genderPreferenceLib = require('../lib/gender-preference');
const { findLinkedPillarKeywords } = require('../lib/pillar-keywords');
const { findLinkedExploreKeywords } = require('../lib/explore-keywords');
const { sendSocketEvent } = require('../lib/socket');
const { getRegion } = require('../lib/country-regions');
const geoip = require('geoip-lite');
const projections = require('../lib/projections');
const { cloudwatch } = require('../lib/cloudwatch');
const PostReport = require('../models/post-report');
const openai = require('../lib/openai');
const PostModeration = require('../models/post-moderation');
const InterestPoint = require('../models/interest-point');
const Profile = require('../models/profile')
const { excludedUniverseLinkingKeyWords } =require('../lib/exclude-from-universe-programmatic-linking')
const { processGifUrlForDownsize } = require('./gif')
const { readPreference, replicaTags } = require('../lib/read-preference-analytics');
const { executeAggregationWithRetry } = require('../lib/retry-aggregate-query')
const personalityLib = require('./personality');
const { isAppUser } = require('../lib/basic');
const postModerationKeywords = require('./post-moderation-keywords');

function useProfilePreview(user) {
  if (!user) {
    // anonymous web
    return true;
  }
  if (user.os == 'web') {
    return true;
  }
  if (user.versionAtLeast('1.13.12')) {
    return true;
  }
  return false;
}

const hideFromProjection = {
  hideFromKeywords: 1,
  keywords: 1,
  hideFromNearby: 1,
  latitude2: 1,
  longitude2: 1,
}

const createdByProjection = {
  ...projections.fullProfileFieldsObj,
  ...hideFromProjection,
  anonymousProfileNickname: 1,
}
const createdByProfilePreviewProjection = {
  ...projections.profilePreviewProjection,
  ...hideFromProjection,
  karma: 1,
  location: 1,
  shadowBanned: 1,
  incomingRequestsPreferences: 1,
  countryCode: 1,
  preferences: 1,
  interestPoints: 1,
  anonymousProfileNickname: 1,
};

function formatProfilePreview(user, requestingUser) {
  if (!user) {
    return null;
  }

  let nearby;
  if (requestingUser
    && user._id != requestingUser._id
    && isLocal(user, requestingUser)
  ) {
    nearby = true;
  }

  return {
    _id: user._id,
    firstName: user.firstName,
    picture: user.picture || (user.pictures ? user.pictures[0] : undefined),
    personality: {
      mbti: user.personality?.mbti,
    },
    enneagram: user.enneagram,
    horoscope: user.horoscope,
    karma: user.karma,
    gender: user.gender,
    age: user.age,
    handle: user.handle,
    nearby,
  };
}

function urlify(s) {
  // Trim string
  s = s.trim();

  // only unicode letters and numbers allowed
  s = s.replace(/[^\p{L}\p{N}\s-]/gu, '');

  // replace all runs of whitespace with a single dash
  s = s.replace(/\s+/g, '-');

  // replace all runs of dash with a single dash
  s = s.replace(/-+/g, '-');

  // convert to lowercase
  return s.toLowerCase();
}

const maxSlugLength = 50;
function createSlug(s) {
  if (!s) {
    return '';
  }
  s = urlify(s);
  if (s.length < maxSlugLength) {
    return s;
  }
  let index = maxSlugLength;
  for (let i = 0; i < maxSlugLength; i++) {
    if (s[i] === '-') {
      index = i;
    }
  }
  return s.substring(0, index);
}

const POPULAR = 'popular';
const RISING = 'rising';
const RECENT = 'recent';
const NEARBY = 'nearby';
const NOT_NEARBY = 'not_nearby';
const TOP_ALL_TIME = 'topAllTime';
const TOP_YEAR = 'topYear';
const TOP_MONTH = 'topMonth';
const TOP_WEEK = 'topWeek';

const forYouNearbyMultiplier = 12;
const forYouSameCountryMultiplier = 8;
const forYouSameRegionMultiplier = 4;
const forYouSameInterestsMultiplier = 4;

function getWeightedScore(post, user, sort, countryCode, multipliers) {
  const region = getRegion(countryCode);

  let score;
  if (sort == 'popular_no_image_multiplier') {
    score = post.scoreNoImageMultiplier;
  } else {
    score = post.score;
  }

  if (multipliers.prioritizeNearby && user && user.location
    && post.userAttributes?.city == user.city
    && post.userAttributes?.state == user.state
    && post.userAttributes?.countryCode == user.countryCode
  ) {
    return score * forYouNearbyMultiplier;
  }
  if (multipliers.prioritizeSameCountry && post.userAttributes?.countryCode == countryCode) {
    return score * forYouSameCountryMultiplier;
  }
  if (multipliers.prioritizeSameRegion && post.region == region) {
    return score * forYouSameRegionMultiplier;
  }
  if (multipliers.prioritizeSameInterests && user.interestNames && user.interestNames.includes(post.interestName)) {
    return score * forYouSameInterestsMultiplier;
  }

  return score;
}


function formatId(id) {
  return id ? new mongoose.Types.ObjectId(id) : null;
}


function isPostVisible(post, createdBy, user) {
  if (post.createdAt > new Date()) {
    return false;
  }
  if (post.interestName == 'questions' && !post.question) {
    // question of the day
    return true;
  }
  if (!createdBy) {
    return false;
  }
  if (post.block && post.block.length > 0) {
    return false;
  }
  if (post.social_block && post.social_block.length > 0 && post.social_block[0].to != user?._id) {
    return false;
  }
  if ((createdBy.banned || createdBy.shadowBanned)
      && (!user || user._id != createdBy._id)) {
    return false;
  }
  if (post.banned && user?._id !== createdBy._id) {
    return false;
  }
  if (post.repliedTo
      && (post.repliedTo.banned || post.repliedTo.shadowBanned)
      && (!user || user._id != post.repliedTo._id)) {
    return false;
  }
  if (user && user._id != createdBy._id) {
    if (createdBy.hideFromKeywords?.length > 0 && user.keywords) {
      if (createdBy.hideFromKeywords.some(x => user.keywords.includes(x))) {
        return false;
      }
    }
    if (user.hideFromKeywords?.length > 0 && createdBy.keywords) {
      if (user.hideFromKeywords.some(x => createdBy.keywords.includes(x))) {
        return false;
      }
    }
    if (user.location && (createdBy.hideFromNearby || user.hideFromNearby)) {
      const nearbyQuery = locationLib.getLatLongQuery(user.location, constants.hideFromNearbyDistance);
      if (createdBy.latitude2 < nearbyQuery.latitude2.$lt && createdBy.latitude2 > nearbyQuery.latitude2.$gt
        && createdBy.longitude2 < nearbyQuery.longitude2.$lt && createdBy.longitude2 > nearbyQuery.longitude2.$gt) {
        return false;
      }
    }
  }
  return true;
}

const createdByDefaultFilter = { 'metrics.numQuestions': { $gt: 0 } };


function buildMatchFilter(user) {
  const preferences = user.socialPreferences;
  const filter = {};

  const filterMinAge = preferences.minAge && preferences.minAge > 18;
  const filterMaxAge = preferences.maxAge && preferences.maxAge < 75;

  if(filterMinAge||filterMaxAge){
    const ageFilter={};
    if (filterMinAge) {
      ageFilter.$gte=preferences.minAge;
    }
    if (filterMaxAge) {
      ageFilter.$lte=preferences.maxAge;
    }
    filter['age'] = ageFilter;
  }

  if (user.birthday && filter.age) {
    const curAge = moment().diff(user.birthday, 'years');
    filter['preferences.maxAge'] = { $gte: curAge };
    filter['preferences.minAge'] = { $lte: curAge };
  }

  const filterDating = preferences.dating && preferences.dating.length > 0 && preferences.dating.length < 3;
  const filterFriends = preferences.friends && preferences.friends.length > 0 && preferences.friends.length < 3;
  if (filterDating || filterFriends) {
    filter['genderPreferenceHash'] = {
      $in: genderPreferenceLib.getCompatibleGenderPreferenceHashes(genderPreferenceLib.hashGenderPreference(user.gender, preferences.dating, preferences.friends))
    };
  }

  if (preferences.showVerifiedOnly && user.isVerified()) {
    filter['verification.status'] = { $in: ['verified', 'reverifying'] };
  }
  if (preferences.personality && preferences.personality.length > 0 && preferences.personality.length < 16) {
    filter['personality.mbti'] = { $in: preferences.personality };
  }
  if (preferences.countries && preferences.countries.length > 0) {
    filter['countryCode'] = { $in: preferences.countries };
  }
  if (preferences.enneagrams && preferences.enneagrams.length > 0) {
    filter['enneagram'] = { $in: preferences.enneagrams }
  }
  if (preferences.horoscopes && preferences.horoscopes.length > 0) {
    filter['horoscope']={ $in: preferences.horoscopes };
  }
  return filter;
}

function getArrHash(arr, charCount) {
  let hash = '';
  arr = [...arr].sort();
  if (charCount) {
    arr.forEach((elem) => { hash += '-' + elem.slice(0, charCount) });
  }
  else { arr.forEach((elem) => { hash += '-' + elem }) };
  return hash;
}


function getUserAttributesFilter(user, preferences) {
  const filters = [];

  const filterMinAge = preferences.minAge && preferences.minAge > 18;
  const filterMaxAge = preferences.maxAge && preferences.maxAge < 75;
  if (filterMinAge || filterMaxAge) {
    const ageFilters = [];
    for (let i = preferences.minAge; i <= preferences.maxAge && i <= 100; i++) {
      ageFilters.push(i);
    }
    filters.push({
      'userAttributes.age': { $in: ageFilters },
    });
  }

  if (user.birthday && (filterMinAge || filterMaxAge)) {
    filters.push({
      'userAttributes.maxAge': {
        $gte: moment().diff(user.birthday, 'years'),
      },
    });
    filters.push({
      'userAttributes.minAge': {
        $lte: moment().diff(user.birthday, 'years'),
      },
    });
  }

  const filterDating = preferences.dating && preferences.dating.length > 0 && preferences.dating.length < 3;
  const filterFriends = preferences.friends && preferences.friends.length > 0 && preferences.friends.length < 3;
  if (filterDating || filterFriends) {
    filters.push({
      'userAttributes.genderPreferenceHash': { $in: genderPreferenceLib.getCompatibleGenderPreferenceHashes(genderPreferenceLib.hashGenderPreference(user.gender, preferences.dating, preferences.friends)) },
    });
  }

  if (preferences.showVerifiedOnly && user.isVerified()) {
    filters.push({
      'userAttributes.status': { $in: ['verified', 'reverifying'] },
    });
  }
  if (preferences.personality && preferences.personality.length > 0 && preferences.personality.length < 16) {
    filters.push({
      'userAttributes.mbti': { $in: preferences.personality },
    });
  }
  if (preferences.countries && preferences.countries.length > 0) {
    filters.push({
      'userAttributes.countryCode': { $in: preferences.countries },
    });
  }
  if (preferences.enneagrams && preferences.enneagrams.length > 0) {
    filters.push({
      'userAttributes.enneagram': { $in: preferences.enneagrams },
    });
  }
  if (preferences.horoscopes && preferences.horoscopes.length > 0) {
    filters.push({
      'userAttributes.horoscope': { $in: preferences.horoscopes },
    });
  }

  return filters;
}

async function getPostsHelper(params) {
  let {
    user,
    matchBy,
    sortBy,
    sortCriteria,
    before,
    reverse,
    lookupSteps,
    pageSize,
    model,
    showAll,
    lookupQuestion,
    atlasSearchQuery,
  } = params;


  // make copy
  matchBy = Object.assign({}, matchBy);
  matchBy.mediaUploadPending = { $ne: true };

  let posts = [];
  while (!posts.length) {
    // set up commands
    if (sortCriteria === RECENT) {
      if (!matchBy.createdAt) {
        matchBy.createdAt = {};
      }
      matchBy.createdAt.$lte = new Date();
    } else if (sortCriteria === POPULAR) {
      if (!matchBy.score) {
        matchBy.score = {};
      }
      matchBy.score.$gte = 0;
    } else if (sortCriteria == 'popular_no_image_multiplier') {
      if (!matchBy.scoreNoImageMultiplier) {
        matchBy.scoreNoImageMultiplier = {};
      }
      matchBy.scoreNoImageMultiplier.$gte = 0;
    } else if (sortCriteria == TOP_ALL_TIME) {
      if (!matchBy.nonDecayedScore) {
        matchBy.nonDecayedScore = {};
      }
      matchBy.nonDecayedScore.$gte = 0;
    } else if (sortCriteria == TOP_YEAR) {
      if (!matchBy.scoreYear) {
        matchBy.scoreYear = {};
      }
      matchBy.scoreYear.$gte = 0;
    } else if (sortCriteria == TOP_MONTH) {
      if (!matchBy.scoreMonth) {
        matchBy.scoreMonth = {};
      }
      matchBy.scoreMonth.$gte = 0;
    } else if (sortCriteria == TOP_WEEK) {
      if (!matchBy.scoreWeek) {
        matchBy.scoreWeek = {};
      }
      matchBy.scoreWeek.$gte = 0;
    } else if (sortCriteria == RISING) {
      if (!matchBy.score) {
        matchBy.score = {};
      }
      matchBy.score.$gte = 0;

      if (!matchBy.nonDecayedScore) {
        matchBy.nonDecayedScore = {};
      }
      matchBy.nonDecayedScore.$gte = 0;
      matchBy.nonDecayedScore.$lt = 10;
    }

    if (before) {
      const operator = reverse ? '$gt' : '$lt';
      if (sortCriteria === RECENT) {
        matchBy.createdAt[operator] = new Date(before);
      } else if (sortCriteria == POPULAR) {
        matchBy.score[operator] = before;
      } else if (sortCriteria == 'popular_no_image_multiplier') {
        matchBy.scoreNoImageMultiplier[operator] = before;
      } else if (sortCriteria == RISING) {
        matchBy.score[operator] = before;
      } else if (sortCriteria == TOP_ALL_TIME) {
        matchBy.nonDecayedScore[operator] = before;
      } else if (sortCriteria == TOP_YEAR) {
        matchBy.scoreYear[operator] = before;
      } else if (sortCriteria == TOP_MONTH) {
        matchBy.scoreMonth[operator] = before;
      } else if (sortCriteria == TOP_WEEK) {
        matchBy.scoreWeek[operator] = before;
      }
    }

    let unfilteredPosts = []

    let commands = []
    if (['beta','prod'].includes(process.env.NODE_ENV) && (atlasSearchQuery != null && atlasSearchQuery['$search'])) {
      delete matchBy.mediaUploadPending
      delete matchBy.postedAnonymously

      sortBy = null
      commands = [
        { $search: atlasSearchQuery['$search'] },
      ];
      if (matchBy && Object.keys(matchBy).length > 0) {
        commands.push({ $match: matchBy });
      }

      commands = commands.concat(lookupSteps);
      if (pageSize) {
        // larger page size for web routes
        commands.push({ $limit: user ? pageSize : 2 * pageSize });
      }
      const start = new Date().getTime();
      unfilteredPosts = await executeAggregationWithRetry(model, commands, {}, { readPreference, replicaTags })
      const end = new Date().getTime();
      console.log(`user ${user?._id} Time to run social feed query: ${end-start} ms.`);
      console.log(`user ${user?._id} unfilteredPosts length : `, unfilteredPosts.length)
    }else{
      commands.push({ $match: matchBy });

      if (sortBy) {
        commands.push({ $sort: sortBy });
      }

      commands = commands.concat(lookupSteps);
      if (pageSize) {
        // larger page size for web routes
        commands.push({ $limit: user ? pageSize : 2 * pageSize });
      }

      // get posts
      unfilteredPosts = await model.aggregate(commands).read(readPreference, replicaTags).option({maxTimeMS: 180000});

    }

    // if no more posts, then break
    if (!unfilteredPosts.length) {
      break;
    }


    // filter the posts and update before in case we need to repeat
    posts = unfilteredPosts.filter((comment) => {
      if (showAll) {
        return true;
      }
      if (lookupQuestion) {
        if (
          !comment.populatedQuestion
          || !isPostVisible(
            comment.populatedQuestion,
            comment.populatedQuestion.populatedCreatedBy,
            user,
          )
          || (
            comment.populatedParent
            && !isPostVisible(
              comment.populatedParent,
              comment.populatedParent.populatedCreatedBy,
              user,
            )
          )
        ) {
          return false;
        }
      }
      return isPostVisible(comment, comment.createdBy, user);
    });
    if (sortCriteria === POPULAR) {
      before = unfilteredPosts[unfilteredPosts.length - 1].score;
    } else if (sortCriteria == 'popular_no_image_multiplier') {
      before = unfilteredPosts[unfilteredPosts.length - 1].scoreNoImageMultiplier;
    } else if (sortCriteria === RISING) {
      before = unfilteredPosts[unfilteredPosts.length - 1].score;
    } else if (sortCriteria === RECENT) {
      before = unfilteredPosts[unfilteredPosts.length - 1].createdAt;
    } else if (sortCriteria === TOP_ALL_TIME) {
      before = unfilteredPosts[unfilteredPosts.length - 1].nonDecayedScore;
    } else if (sortCriteria === TOP_YEAR) {
      before = unfilteredPosts[unfilteredPosts.length - 1].scoreYear;
    } else if (sortCriteria === TOP_MONTH) {
      before = unfilteredPosts[unfilteredPosts.length - 1].scoreMonth;
    } else if (sortCriteria === TOP_WEEK) {
      before = unfilteredPosts[unfilteredPosts.length - 1].scoreWeek;
    }

    if (!before) {
      break;
    }

    // if we did not limit, then no need to repeat
    if (!pageSize) {
      break;
    }
  }
  return posts;
}

async function getPosts(user, sortCriteria, reverse, originalMatchBy, before, pageSize, model, formatFn, lookupQuestion, showAll, applyPreferenceFilters, isFriendsFeed, showFriendComments, videosOnly, countryCode, filter, multipliers, atlasSearchQuery) {

  if (!sortCriteria) {
    sortCriteria = '';
  }
  const matchBy = { ...originalMatchBy };

  if (lookupQuestion) {
    matchBy.question = { $ne: null };
  }

  if (videosOnly) {
    matchBy.isVideo = true;
    matchBy.convertedVideo = { $ne: null };
  }

  const customFeed = user?.findCustomFeed(filter);
  if (customFeed) {
    if (customFeed.relationship == 'friends') {
      const friendIds = await FriendList.getFriendIds(user._id);
      matchBy.createdBy = { $in: (friendIds || []) };
    }
    else if (customFeed.relationship == 'following') {
      const followingUserIds = await Follow.distinct(
        'to',
        { from: user._id },
      );
      matchBy.createdBy = { $in: (followingUserIds || []) };
    }

    if (customFeed.interestNames || customFeed.keywords) {
      const orArr = [];
      if (customFeed.interestNames && customFeed.interestNames.length > 0) {
        orArr.push({ interestName: { $in: customFeed.interestNames } });
      }
      if (customFeed.keywords && customFeed.keywords.length > 0) {
        orArr.push({ keywords: { $in: customFeed.keywords } });
      }
      if (orArr.length > 0) {
        matchBy.$or = orArr;
      }
    }

    if (customFeed.excludeKeywords) {
      matchBy.keywords = { $nin: customFeed.excludeKeywords };
    }

    const userAttributesFilter = getUserAttributesFilter(user, customFeed);
    if (userAttributesFilter.length > 0) {
      matchBy.$and = userAttributesFilter;
    };
  }
  if (filter == 'for_you') {
    matchBy['userAttributes.genderPreferenceHash'] = { $in: genderPreferenceLib.getCompatibleGenderPreferenceHashes(user.genderPreferenceHash) };

    const preferences = user.preferences;
    const filterMinAge = preferences.minAge && preferences.minAge > 18;
    const filterMaxAge = preferences.maxAge && preferences.maxAge < 75;
    if (filterMinAge || filterMaxAge) {
      const ageFilters = [];
      for (let i = preferences.minAge; i <= preferences.maxAge && i <= 100; i++) {
        ageFilters.push(i);
      }
      matchBy['userAttributes.age'] = { $in: ageFilters };
    }

    if (user.age) {
      matchBy['userAttributes.maxAge'] = { $gte: user.age };
      matchBy['userAttributes.minAge'] = { $lte: user.age };
    }
  }

  if (applyPreferenceFilters && user && user.socialPreferences && user.socialPreferencesActivated && !isFriendsFeed && !customFeed && !user.versionAtLeast('1.13.0')) {
    const userAttributesFilter = getUserAttributesFilter(user, user.socialPreferences);
    if (userAttributesFilter.length > 0) {
      matchBy.$and = userAttributesFilter;
    };
  }

  if ([NEARBY, NOT_NEARBY].includes(sortCriteria)) {
    if (user.location) {
      const maxDistance = 50 * 1609;
      let nearbyUserIds;

      const query = {
        location: {
          $nearSphere: {
            $geometry: user.location,
            $minDistance: 0,
            $maxDistance: maxDistance, // miles to meters
          },
        },
        ...createdByDefaultFilter,
      };

      if (model === Question && sortCriteria == NEARBY) {
        //exclude user own post if sort is nearby
        query._id = { $ne: user._id };
      }

      nearbyUserIds = await User.distinct('_id', query).read(readPreference, replicaTags)
      if (sortCriteria == NEARBY) {
        if (nearbyUserIds.length === 0) {
          return [];
        }
        matchBy.createdBy = { $in: nearbyUserIds };
      } else {
        matchBy.createdBy = { $nin: nearbyUserIds };
      }
    }

    sortCriteria = RECENT;
  }

  const reverseMultiplier = reverse ? -1 : 1;
  let sortBy;
  if (sortCriteria === RECENT) {
    sortBy = { createdAt: -1 * reverseMultiplier };
  } else if (sortCriteria === POPULAR) {
    sortBy = { score: -1 * reverseMultiplier };
  } else if (sortCriteria == 'popular_no_image_multiplier') {
    sortBy = { scoreNoImageMultiplier: -1 * reverseMultiplier };
  } else if (sortCriteria === TOP_ALL_TIME) {
    sortBy = { nonDecayedScore: -1 * reverseMultiplier };
  } else if (sortCriteria === TOP_YEAR) {
    sortBy = { scoreYear: -1 * reverseMultiplier };
  } else if (sortCriteria === TOP_MONTH) {
    sortBy = { scoreMonth: -1 * reverseMultiplier };
  } else if (sortCriteria === TOP_WEEK) {
    sortBy = { scoreWeek: -1 * reverseMultiplier };
  } else if (sortCriteria == RISING) {
    sortBy = { score: -1 };
  }

  let lookupSteps = [];

  if (user && model === Comment) {
    const friendIds = await FriendList.getFriendIds(user._id);

    if (showFriendComments == true) {
      matchBy.createdBy = { $in: friendIds };
    }
    if (showFriendComments == false) {
      matchBy.createdBy = { $nin: friendIds };
    }

    lookupSteps.push({
      $addFields: {
        isFriendComment: { $in: ['$createdBy', friendIds] },
      },
    });
  }

  // exclude question.postedAnonymously = true for false group and non app user
  /*
  if (!user || !user.versionAtLeast('1.13.74') || !isAppUser(user)){
    matchBy.postedAnonymously = { $ne: true };
  }
  */
  // temporarily remove anonymous feature
  matchBy.postedAnonymously = { $ne: true };

  lookupSteps = lookupSteps.concat([
    {
      $lookup: {
        from: 'users',
        let: { "createdBy": "$createdBy" },
        pipeline: [
          { "$match": { "$expr": { "$eq": ["$_id", "$$createdBy"] }}},
          { "$project": useProfilePreview(user) ? createdByProfilePreviewProjection : createdByProjection }
        ],
        as: 'createdByObj',
      },
    },
    {
      $addFields: {
        createdByObj: {
          $arrayElemAt: ['$createdByObj', 0],
        },
      },
    },
  ]);

  if (user) {
    lookupSteps = lookupSteps.concat([
      {
        $addFields: {
          hasUserLiked: { $in: [user._id, '$usersThatLiked'] },
          'poll.optionVotedByUser': `$poll.votes.${user._id}`,
        },
      },
      getBlockLookup(user._id),
      {
        $lookup: {
          from: 'hideonsocials',
          let: {
            me: user._id,
            createdBy: '$createdBy',
          },
          pipeline: [
            {
              $match:
              {
                $expr:
                {
                  $and:
                  [
                    { $eq: ['$from', '$$me'] },
                    { $eq: ['$to', '$$createdBy'] },
                    /*
                    // This $ne condition is causing a collection scan,
                    // preventing the from_1_to_1 index from being used.
                    // This condition is probably unnecessary because
                    // we don't allow users to hide themselves.
                    { $ne: ['$$me', '$$createdBy'] },
                    */
                  ],
                },
              },
            },
          ],
          as: 'social_block',
        },
      },
      /*
      // Explain shows that this stage results in more keys/docs examined
      // even if the user has never blocked anyone.
      {
        $match: {
          'social_block.0': { $exists: false },
        },
      },
      */
    ]);
  } else {
    lookupSteps = lookupSteps.concat([
      {
        $addFields: {
          hasUserLiked: false,
        },
      },
    ]);
  }

  if (model === Question && user && user.versionAtLeast('1.11.59')) {
    const friendIds = await FriendList.getFriendIds(user._id);

    lookupSteps = lookupSteps.concat([
      {
        $lookup: {
          from: 'users',
          localField: 'usersThatCommented',
          foreignField: '_id',
          let: {
            usersWithBannedComments: { $ifNull: ['$usersWithBannedComments', []] }
           },
          pipeline: [
            {
              $match: {
                _id: { $in: friendIds },
                shadowBanned: { $ne: true },
              },
            },
            {
              $match: {
                $expr: {
                  $not: { $in: ['$_id', '$$usersWithBannedComments'] },
                },
              },
            },
            { $project: { _id: 1, firstName: 1, picture: { $first: '$pictures' } } },
          ],
          as: 'friendsThatCommented',
        },
      },
      {
        $set: {
          friendsThatCommented: {
            $cond: {
              if: { $gt: ['$createdAt', new Date('2022-09-16T10:30:00')] },
              then: '$friendsThatCommented',
              else: [],
            },
          },
        },
      },
    ]);

    if (isFriendsFeed) {
      lookupSteps = lookupSteps.concat([
        {
          $match: {
            $or: [
              { createdBy: { $in: friendIds } },
              { 'friendsThatCommented.0': { $exists: true } },
            ],
          },
        },
      ]);
    }
  }

  if (user && model == Question) {
    lookupSteps = lookupSteps.concat([
      {
        $lookup: {
          from: 'savedquestions',
          let: {
            user: user._id,
            question: '$_id',
          },
          pipeline: [
            {
              $match:
              {
                $expr:
                {
                  $and:
                  [
                    { $eq: ['$user', '$$user'] },
                    { $eq: ['$question', '$$question'] },
                  ],
                },
              },
            },
          ],
          as: 'saved',
        },
      },
      {
        $addFields: {
          hasUserSaved: { $gt: [{ $size: '$saved' }, 0] },
        },
      },
    ]);
  } else {
    lookupSteps = lookupSteps.concat([
      {
        $addFields: {
          hasUserSaved: false,
        },
      },
    ]);
  }

  if (lookupQuestion) {
    lookupSteps = lookupSteps.concat([
      {
        $lookup: {
          from: 'questions',
          let: {
            questionId: '$question',
          },
          pipeline: [
            { $match: { $expr: { $eq: ['$_id', '$$questionId'] } } },
            {
              $lookup: {
                from: 'users',
                let: {
                  createdById: '$createdBy',
                },
                pipeline: [
                  { $match: { $expr: { $eq: ['$_id', '$$createdById'] } } },
                  { $project: { _id: 1, banned: 1, shadowBanned: 1, ...hideFromProjection } },
                ],
                as: 'populatedCreatedBy',
              },
            },
            {
              $addFields: {
                populatedCreatedBy: { $arrayElemAt: ['$populatedCreatedBy', 0] },
              },
            },
            getBlockLookup(user._id),
            {
              $project: {
                banned: 1,
                populatedCreatedBy: 1,
                block: 1,
                parent: 1,
                interestName: 1,
                title: 1,
                text: 1,
                webId: 1,
              },
            },
          ],
          as: 'populatedQuestion',
        },
      },
      {
        $addFields: {
          populatedQuestion: { $arrayElemAt: ['$populatedQuestion', 0] },
        },
      },
      {
        $lookup: {
          from: 'comments',
          let: {
            commentId: '$parent',
          },
          pipeline: [
            { $match: { $expr: { $eq: ['$_id', '$$commentId'] } } },
            {
              $lookup: {
                from: 'users',
                let: {
                  createdById: '$createdBy',
                },
                pipeline: [
                  { $match: { $expr: { $eq: ['$_id', '$$createdById'] } } },
                  { $project: { _id: 1, banned: 1, shadowBanned: 1, ...hideFromProjection } },
                ],
                as: 'populatedCreatedBy',
              },
            },
            {
              $addFields: {
                populatedCreatedBy: { $arrayElemAt: ['$populatedCreatedBy', 0] },
              },
            },
            getBlockLookup(user._id),
            {
              $project: {
                banned: 1,
                populatedCreatedBy: 1,
                block: 1,
                parent: 1,
              },
            },
          ],
          as: 'populatedParent',
        },
      },
      {
        $addFields: {
          populatedParent: { $arrayElemAt: ['$populatedParent', 0] },
        },
      },
    ]);
  }

  lookupSteps = lookupSteps.concat([
    {
      $lookup: {
        from: 'users',
        localField: 'repliedTo',
        foreignField: '_id',
        as: 'repliedTo',
      },
    },
    {
      $project: {
        createdAt: 1,
        createdBy: '$createdByObj',
        question: 1,
        profile: 1,
        title: 1,
        text: 1,
        gif: 1,
        image: 1,
        images: 1,
        convertedVideo: 1,
        aspectRatio: 1,
        altText: 1,
        audio: 1,
        audioWaveform: 1,
        audioDuration: 1,
        parent: 1,
        interestName: 1,
        repliedTo: {
          $arrayElemAt: ['$repliedTo', 0],
        },
        repliedToIsAnonymous: 1,
        depth: 1,
        numComments: 1,
        numAnonymousComments: 1,
        friendsThatCommented: 1,
        numLikes: 1,
        numViews: 1,
        isDeleted: 1,
        isEdited: 1,
        isBoosted: 1,
        banned: 1,
        bannedReason: 1,
        hasUserLiked: 1,
        hasUserSaved: 1,
        block: 1,
        social_block: 1,
        score: 1,
        scoreNoImageMultiplier: 1,
        nonDecayedScore: 1,
        scoreYear: 1,
        scoreMonth: 1,
        scoreWeek: 1,
        webId: 1,
        populatedQuestion: 1,
        populatedParent: 1,
        language: 1,
        postRepliedTo: 1,
        awards: 1,
        'poll.options': 1,
        'poll.optionVotedByUser': 1,
        vote: 1,
        isFriendComment: 1,
        linkedKeywords: 1,
        linkedExploreKeywords: 1,
        linkedPillarKeywords: 1,
        linkedCategories: 1,
        linkedSubcategories: 1,
        linkedProfiles: 1,
        mentionedUsersTitle: 1,
        mentionedUsersText: 1,
        hashtags: 1,
        postedAnonymously: 1
      },
    },
  ]);


  let posts;

  const region = getRegion(countryCode);
  if (countryCode && region) {

    let scoreField;
    if (sortCriteria == 'popular_no_image_multiplier') {
      scoreField = 'scoreNoImageMultiplier';
    } else {
      scoreField = 'score';
    }

    let nearbyPosts = [];
    let countryPosts = [];
    let regionPosts = [];
    let interestPosts = [];
    let otherPosts = [];

    if (multipliers.prioritizeNearby && user?.location) {

      matchBy['userAttributes.countryCode'] = user.countryCode;
      matchBy['userAttributes.state'] = user.state;
      matchBy['userAttributes.city'] = user.city;
      nearbyPosts = await getPostsHelper({
        user,
        matchBy,
        sortBy,
        sortCriteria,
        before: before / forYouNearbyMultiplier,
        reverse,
        lookupSteps,
        pageSize,
        model,
        showAll,
        lookupQuestion,
        atlasSearchQuery,
      });
      nearbyPosts = nearbyPosts.map(function(x) {
        x.score = forYouNearbyMultiplier * x[scoreField];
        return x;
      });

      delete matchBy['userAttributes.countryCode'];
      delete matchBy['userAttributes.state'];
      matchBy['userAttributes.city'] = { $ne: user.city };
    }

    if (multipliers.prioritizeSameCountry) {
      matchBy['userAttributes.countryCode'] = countryCode;
      countryPosts = await getPostsHelper({
        user,
        matchBy,
        sortBy,
        sortCriteria,
        before: before / forYouSameCountryMultiplier,
        reverse,
        lookupSteps,
        pageSize,
        model,
        showAll,
        lookupQuestion,
        atlasSearchQuery,
      });
      countryPosts = countryPosts.map(function(x) {
        x.score = forYouSameCountryMultiplier * x[scoreField];
        return x;
      });
      matchBy['userAttributes.countryCode'] = { $ne: countryCode };
    }

    if (multipliers.prioritizeSameRegion) {
      matchBy['region'] = region;
      regionPosts = await getPostsHelper({
        user,
        matchBy,
        sortBy,
        sortCriteria,
        before: before / forYouSameRegionMultiplier,
        reverse,
        lookupSteps,
        pageSize,
        model,
        showAll,
        lookupQuestion,
        atlasSearchQuery,
      });
      regionPosts = regionPosts.map(function(x) {
        x.score = forYouSameRegionMultiplier * x[scoreField];
        return x;
      });
      matchBy['region'] = { $ne: region };
    }

    if (multipliers.prioritizeSameInterests) {
      if (user?.interestNames && user.interestNames.length > 0) {
        matchBy['interestName']['$in'] = user.interestNames;
        interestPosts = await getPostsHelper({
          user,
          matchBy,
          sortBy,
          sortCriteria,
          before: before / forYouSameInterestsMultiplier,
          reverse,
          lookupSteps,
          pageSize,
          model,
          showAll,
          lookupQuestion,
          atlasSearchQuery,
        });
        interestPosts = interestPosts.map(function(x) {
          x.score = forYouSameInterestsMultiplier * x[scoreField];
          return x;
        });

        delete matchBy['interestName']['$in'];
        matchBy['interestName']['$nin'] = (matchBy['interestName']['$nin'] || []).concat(user.interestNames);
      }
    }

    otherPosts = await getPostsHelper({
      user,
      matchBy,
      sortBy,
      sortCriteria,
      before,
      reverse,
      lookupSteps,
      pageSize,
      model,
      showAll,
      lookupQuestion,
      atlasSearchQuery,
    });

    posts = nearbyPosts.concat(countryPosts).concat(regionPosts).concat(interestPosts).concat(otherPosts)
    posts = posts.sort((a, b) => b.score - a.score).filter((v,i,a)=>a.findIndex(v2=>(v2._id===v._id))===i).slice(0, pageSize)
  }
  else {
    posts = await getPostsHelper({
      user,
      matchBy,
      sortBy,
      sortCriteria,
      before,
      reverse,
      lookupSteps,
      pageSize,
      model,
      showAll,
      lookupQuestion,
      atlasSearchQuery,
    });
  }

  if (reverse) {
    posts.reverse();
  }

  const start = new Date().getTime();
  posts = posts.map((c) => formatFn(c, user));
  const end = new Date().getTime();
  console.log(`User ${user?._id} Time to format posts: ${end-start} ms. Posts length: ${posts.length}`);

  return posts;
}

async function getLikes(id, pageNumber, model, requestingUser) {
  pageNumber += 1;
  const pageSize = constants.getPageSize();
  const position = -1 * pageNumber * pageSize;
  const uid = requestingUser._id;

  const comments = await model.aggregate([
    {
      $match: {
        _id: new mongoose.Types.ObjectId(id),
        createdBy: uid,
      },
    },
    {
      $project: {
        numLikes: 1,
        usersThatLikedIds: { $slice: ['$usersThatLiked', position, pageSize] },
      },
    },
    {
      $unwind:{
        path:'$usersThatLikedIds',
      }
    },
    {
      $lookup: {
        from: 'users',
        let: {
          userId: '$usersThatLikedIds',
        },
        pipeline: [
          {
            $match: {
              $expr: { $eq: ['$_id', '$$userId'] },
            },
          },
          { $project: projections.fullProfileFieldsObj },
          getBlockLookup('$_id', uid),
        ],
        as: 'userThatLiked',
      },
    },
    {
      $group:{
        _id:'$_id',
        usersThatLiked: { $push: { $arrayElemAt: ['$userThatLiked', 0] } },
        numLikes: { $first: '$numLikes' }
      }
    }
  ]);
  if (comments.length == 0) {
    return {
      totalPages: 0,
      usersThatLiked: [],
    };
  }
  const comment = comments[0];
  /* const blocks = await Action.find({
    $or:
    [
      {
        $and: [
          { from: uid },
          { to: { $in: comment.usersThatLikedIds } },
          { block: true },
        ],
      },
      {
        $and: [
          { from: { $in: comment.usersThatLikedIds } },
          { to: uid },
          { block: true },
        ],
      },
    ],
  }); */

  const totalPages = Math.ceil(comment.numLikes / pageSize);
  let { usersThatLiked } = comment;

  if (pageNumber > totalPages) {
    usersThatLiked = [];
  }
  if (pageNumber == totalPages) {
    const excess = -1 * position - comment.numLikes;
    usersThatLiked = usersThatLiked.slice(0, pageSize - excess);
  }

  usersThatLiked = usersThatLiked.filter((user) => !user.banned
           && (!user.shadowBanned || user._id == uid)
           && user.block.length===0/* !blocks.some((e) => e.from == uid && e.to == user._id
                  || e.from == user._id && e.to == uid) */);

  return {
    totalPages,
    usersThatLiked: usersThatLiked.reverse().map((user) => formatProfile(user, requestingUser, { isUniverse: true })),
  };
}

const MS_PER_HOUR = 3600000;
const HOURS_PER_HALF_LIFE = 1;
const MAX_NUM_HALF_LIFE = 12; // increase this if scores increase past 4k
const MAX_HOURS_DECAY = HOURS_PER_HALF_LIFE * MAX_NUM_HALF_LIFE;
const HOURS_PER_HALF_LIFE_2 = 24;
const MAX_NUM_HALF_LIFE_2 = 365;
const MAX_HOURS_DECAY_2 = HOURS_PER_HALF_LIFE_2 * MAX_NUM_HALF_LIFE_2;

function getDecayFunction(baseScore, hoursBeforeDecay) {
  return {
    $multiply: [
      baseScore,
      {
        $pow: [
          0.5,
          {
            $min: [
              MAX_NUM_HALF_LIFE,
              {
                $divide: [
                  {
                    $divide: [
                      {
                        $max: [
                          0,
                          {
                            $subtract: [
                              new Date(),
                              {
                                $add: [
                                  '$createdAt',
                                  hoursBeforeDecay * MS_PER_HOUR,
                                ],
                              },
                            ],
                          },
                        ],
                      },
                      MS_PER_HOUR,
                    ],
                  },
                  HOURS_PER_HALF_LIFE,
                ],
              },
            ],
          },
        ],
      },
      {
        $pow: [
          0.9,
          {
            $min: [
              MAX_NUM_HALF_LIFE_2,
              {
                $divide: [
                  {
                    $divide: [
                      {
                        $max: [
                          0,
                          {
                            $subtract: [
                              new Date(),
                              {
                                $add: [
                                  '$createdAt',
                                  hoursBeforeDecay * MS_PER_HOUR,
                                  MAX_HOURS_DECAY,
                                ],
                              },
                            ],
                          },
                        ],
                      },
                      MS_PER_HOUR,
                    ],
                  },
                  HOURS_PER_HALF_LIFE_2,
                ],
              },
            ],
          },
        ],
      },
    ],
  };
}

function getDailyBoostedScore(baseScore) {
  return {
    $multiply: [
      baseScore,
      {
        $cond: {
          if: {
            $or: [
              { $ifNull: ['$isBoosted', false] },
              { $ifNull: ['$awards.ignite', false] }
            ]
          },
          then: 2, // boosted posts have 2x multiplier
          else: 1,
        },
      },
      {
        $cond: {
          if: { $ifNull: ['$isLightlyBoosted', false] },
          then: 1.5,
          else: 1,
        },
      },
    ],
  };
}

function getScorePipeline(isQuestion) {
  const baseScore = {
    $multiply: [
      {
        $add: [
          '$numLikes',
          { $ifNull: ['$numUsersThatCommented', 0] },
          { $rand: {} },
        ],
      },
      {
        $cond: {
          if: { $gt: ['$createdAt', new Date()] },
          then: -1, // future questions have a negative score
          else: 1,
        },
      },
      {
        $cond: {
          if: { $ifNull: ['$noobPost', false] },
          then: 2, // starter posts have a multiplier of 2
          else: 1,
        },
      },
    ],
  };
  const baseScoreNoImageMultiplier = {
    $multiply: [
      {
        $add: [
          '$numLikes',
          { $ifNull: ['$numUsersThatCommented', 0] },
          { $rand: {} },
        ],
      },
      {
        $cond: {
          if: { $gt: ['$createdAt', new Date()] },
          then: -1, // future questions have a negative score
          else: 1,
        },
      },
      {
        $cond: {
          if: { $ifNull: ['$noobPost', false] },
          then: 2, // starter posts have a multiplier of 2
          else: 1,
        },
      },
    ],
  };

  const setOp = { scoreUpdatedAt: '$$NOW' };

  if (isQuestion) {
    // no decay initially, then decay with half-life
    setOp.nonDecayedScore = baseScore;
    setOp.score = getDailyBoostedScore(getDecayFunction(baseScore, 24 * 1));
    setOp.scoreNoImageMultiplier = getDailyBoostedScore(getDecayFunction(baseScoreNoImageMultiplier, 24 * 1));
    setOp.scoreWeek = getDecayFunction(baseScore, 24 * 7);
    setOp.scoreMonth = getDecayFunction(baseScore, 24 * 30);
    setOp.scoreYear = getDecayFunction(baseScore, 24 * 365);
  }
  else {
    setOp.score = getDailyBoostedScore(baseScore);
  };

  return [ { $set: setOp } ];
}

async function updateQuestionScore(id, forceUpdate) {
  let query = { _id: id };
  if (constants.throttleScoreUpdates() && !forceUpdate) {
    query.scoreUpdatedAt = { $not: { $gt: DateTime.utc().minus({ hours: 1 }).toJSDate() } };
  }
  await Question.updateOne(
    query,
    getScorePipeline(true),
  );
}
async function updateCommentScore(id) {
  let query = { _id: id };
  if (constants.throttleScoreUpdates()) {
    query.scoreUpdatedAt = { $not: { $gt: DateTime.utc().minus({ hours: 1 }).toJSDate() } };
  }
  await Comment.updateOne(
    query,
    getScorePipeline(false),
  );
}

async function updateQuestionScores() {
  {
    const res = await Question.updateMany(
      {
        createdAt: {
          $gt: moment().subtract(2, 'days').toDate(),
          $lt: moment().subtract(1, 'days').toDate(),
        }
      },
      getScorePipeline(true),
    );
    console.log('updateQuestionScores daily', res);
  }
  {
    const res = await Question.updateMany(
      {
        createdAt: {
          $gt: moment().subtract(8, 'days').toDate(),
          $lt: moment().subtract(7, 'days').toDate(),
        }
      },
      getScorePipeline(true),
    );
    console.log('updateQuestionScores weekly', res);
  }
  {
    const res = await Question.updateMany(
      {
        createdAt: {
          $gt: moment().subtract(31, 'days').toDate(),
          $lt: moment().subtract(30, 'days').toDate(),
        }
      },
      getScorePipeline(true),
    );
    console.log('updateQuestionScores monthly', res);
  }
  {
    const res = await Question.updateMany(
      {
        createdAt: {
          $gt: moment().subtract(366, 'days').toDate(),
          $lt: moment().subtract(365, 'days').toDate(),
        }
      },
      getScorePipeline(true),
    );
    console.log('updateQuestionScores yearly', res);
  }
}
async function updateAllQuestionScores() {
  const res = await Question.updateMany(
    {},
    getScorePipeline(true),
  );
  console.log('updateAllQuestionScores', res);
}
async function updateCommentScores() {
  const res = await Comment.updateMany(
    {},
    getScorePipeline(false),
  );
  console.log('updateCommentScores', res);
}

const KARMA_SQRT_THRESHOLD = parseInt(process.env.KARMA_SQRT_THRESHOLD || 100);

async function incrementKarma(userId, karma, from, reason) {
  let res;
  if (from && karma > 0) {
    res = await User.updateOne(
      {
        _id: userId,
        'currentDayMetrics.karmaFromLikingPostsPartners': { $ne: from },
      },
      {
        $inc: { karma },
        $addToSet: { 'currentDayMetrics.karmaFromLikingPostsPartners': from },
      },
    );
  }
  else if (from && karma < 0) {
    res = await User.updateOne(
      {
        _id: userId,
        'currentDayMetrics.karmaFromLikingPostsPartners': from,
      },
      {
        $inc: { karma },
        $pull: { 'currentDayMetrics.karmaFromLikingPostsPartners': from },
      },
    );
  }
  else {
    res = await User.updateOne(
      { _id: userId },
      { $inc: { karma } },
    );
  }

  if (!res.modifiedCount) {
    return;
  }

  if (karma < 0) {
    await User.updateOne(
      { _id: userId },
      { $max: { karma: 0 } },
    );
  }

  const user = await User.findOne({ _id: userId });
  const isNewKarmaSystem = false;

  if (karma > 0 && reason) {
    const socketData = {
      karmaGained: karma,
      totalKarma: user.karma,
      reason,
    }
    sendSocketEvent(userId, 'karma', socketData);
  }

  if (!user.appVersion || cmp(user.appVersion, '1.10.36') < 0) {
    return;
  }

  let nextTier;
  let karmaTiersLength;
  let karmaTier;

  if (isNewKarmaSystem) {
    nextTier = user.newKarmaTier + 1;
    karmaTiersLength = getNewKarmaTiers().length;
    karmaTier = getNewKarmaTiers()[nextTier];
  } else {
    nextTier = user.karmaTier + 1;
    karmaTiersLength = getKarmaTiers().length;
    karmaTier = getKarmaTiers()[nextTier];
  }

  if (nextTier < karmaTiersLength && user.karma >= karmaTier) {
    const res = await User.updateOne(
      { _id: userId, karmaTier: user.karmaTier },
      { $set: { ...(isNewKarmaSystem ? { newKarmaTier: nextTier } : { karmaTier: nextTier }) } },
    );
    if (res.modifiedCount) {
      const coins = await coinsLib.updateCoins(
        { user: userId },
        {
          $inc: {
            ...(
              isNewKarmaSystem
                ? { coins: getNewKarmaCoinRewards()[nextTier] }
                : { coins: karmaTier }
            ),
          },
        },
        'karma tier new level',
      );
      const data = {
        karma: user.karma,
        coins,
      };
      admin.sendNotification(
        user,
        null,
        translate('Congrats!', user.locale),
        translate('You\'ve reached a new level!', user.locale),
        { karma: JSON.stringify(data) },
        null,
        'social',
        'new-karma-level',
      );
    }
  }
}

function getKarma(n) {
  // 1 point until reaching threshold, then square root after threshold

  if (!n || isNaN(n)) {
    return 0;
  }
  const t = KARMA_SQRT_THRESHOLD;
  return Math.min(t, n) + Math.sqrt(Math.max(0, n - t));
}

async function incrementUserKarma(userId, current, previous, from) {
  const karma = getKarma(current) - getKarma(previous);
  await incrementKarma(userId, karma, from);
}

async function incrementInterestPoints(userId, interest, language, points) {
  if (!userId || !interest || !language || !points) {
    return;
  }

  let res;

  res = await User.updateOne(
    {
      _id: userId,
      interestPoints: { $elemMatch: { interest, language } },
    },
    {
      $inc: {
        'interestPoints.$.points': points,
      },
    },
  );
  console.log(`incrementInterestPoints(${userId}, ${interest}, ${language}, ${points}) op1`, res);

  if (!res.modifiedCount) {

    // handle case where the record does not exist yet
    res = await User.updateOne(
      {
        _id: userId,
        $nor: [{
          'interestPoints.interest': interest,'interestPoints.language': language,
        }],
      },
      {
        $push: {
          interestPoints: {
            interest,
            language,
            points,
          }
        },
      },
    );
    console.log(`incrementInterestPoints(${userId}, ${interest}, ${language}, ${points}) op2`, res);

    if (!res.modifiedCount) {

      // handle case where the record was inserted by a different operation
      res = await User.updateOne(
        {
          _id: userId,
          interestPoints: { $elemMatch: { interest, language } },
        },
        {
          $inc: {
            'interestPoints.$.points': points,
          },
        },
      );
      console.log(`incrementInterestPoints(${userId}, ${interest}, ${language}, ${points}) op3`, res);
    }
  }

  await InterestPoint.updateOne(
    {
      user: userId,
      interest,
      language,
    },
    {
      $inc: {
        points,
      }
    },
    {
      upsert: true,
    },
  );

  await updateInterestRanks(interest, language);
}

async function updateInterestRanks(interest, language) {
  if (!interest || !language) {
    return;
  }

  {
    if (interest == 'questions') {
      return;
    }
    const interestDoc = await Interest.findOne({ name: interest }, 'eligibleForRanks');
    if (!interestDoc) {
      return;
    }
    if (!interestDoc.eligibleForRanks.get(language)) {
      const usersWhoPosted = await Question.distinct('createdBy', { interestName: interest, language: language });
      if (usersWhoPosted.length >= 10) {
        interestDoc.eligibleForRanks.set(language, true);
        await interestDoc.save();
      } else {
        return;
      }
    }
  }

  const query = {
    interest: interest,
    language: language,
    points: { $gt: 0 },
    shadowBanned: { $ne: true },
  };

  const entries = await InterestPoint
    .find(query)
    .limit(100)
    .sort({ points: -1 })
    .lean()
  console.log(interest, language, JSON.stringify(entries,null,2));

  let bulk = User.collection.initializeUnorderedBulkOp();
  let interestPointBulk = InterestPoint.collection.initializeUnorderedBulkOp();
  let numUpdates = 0;
  for (let i = 0; i < entries.length; i++) {
    const entry = entries[i];
    const priorEntry = entries[i-1];
    let rank;
    if (entry.points == priorEntry?.points) {
      rank = priorEntry.rank;
    } else {
      rank = i+1;
    }
    if (rank != entry.rank) {
      const filter = {
        _id: entry.user,
        interestPoints: { $elemMatch: { interest, language } },
      };
      const updates = {
        $set: {
          'interestPoints.$.rank': rank,
        }
      };
      console.log(`Setting user ${entry.user} rank for ${interest} ${language} to ${rank}`);
      bulk.find(filter).update(updates);
      interestPointBulk.find({user: entry.user, interest, language}).update({$set: {rank}});
      numUpdates++;
      entry.rank = rank;
    }
  }
  if (numUpdates) {
    {
      const res = await bulk.execute();
      console.log(`Updated ${numUpdates} ranks for ${interest} ${language}, User result: `, res);
    }
    {
      const res = await interestPointBulk.execute();
      console.log(`Updated ${numUpdates} ranks for ${interest} ${language}, InterestPoint result: `, res);
    }
  }

  {
    const usersToRemoveRank = await InterestPoint.distinct(
      'user',
      {
        user: { $nin: entries.map(x => x.user) },
        interest: interest,
        language: language,
        rank: { $gt: 0 },
      },
    );
    if (usersToRemoveRank.length) {
      {
        const res = await InterestPoint.updateMany(
          {
            user: { $in: usersToRemoveRank },
            interest: interest,
            language: language,
            rank: { $gt: 0 },
          },
          {
            $unset: {
              rank: '',
            },
          },
        );
        console.log(`Removed outdated ranks for ${interest} ${language}, InterestPoint result: `, res);
      }
      {
        const res = await User.updateMany(
          {
            _id: { $in: usersToRemoveRank },
            interestPoints: { $elemMatch: { interest, language, rank: { $gt: 0 } } },
          },
          {
            $unset: {
              'interestPoints.$.rank': '',
            },
          },
        );
        console.log(`Removed outdated ranks for ${interest} ${language}, User result: `, res);
      }
    }
  }
}

async function backfillUserKarma(userId) {
  const projection = 'numLikes numUsersThatCommented';
  const questions = await Question.find({ createdBy: userId }, projection);
  const comments = await Comment.find({ createdBy: userId }, projection);
  const posts = questions.concat(comments);

  let karma = 0;
  for (const post of posts) {
    karma += getKarma(post.numLikes);
    karma += getKarma(post.numUsersThatCommented);
  }

  await User.updateOne(
    { _id: userId },
    { karma },
  );

  return karma;
}

async function backfillAllUserKarma() {
  const users = mongoose.connection.db.collection('users');
  const query = {};
  const options = {
    projection: {
      _id: 1,
    },
  };
  let i = 0;
  const cursor = users.find(query, options);
  for await (const user of cursor) {
    i += 1;
    const karma = await backfillUserKarma(user._id);
    console.log(i, user._id, karma);
  }
}

async function backfillAllUserKarmaV2() {
  const query = { banned: { $ne: true } };
  const projection = 'createdBy numLikes numUsersThatCommented';
  const questions = await Question.find(query, projection);
  const comments = await Comment.find(query, projection);
  const posts = questions.concat(comments);

  const karmas = {};
  for (const post of posts) {
    const k = getKarma(post.numLikes) + getKarma(post.numUsersThatCommented);
    karmas[post.createdBy] = karmas[post.createdBy] || 0;
    karmas[post.createdBy] += k;
  }

  for (const [userId, karma] of Object.entries(karmas)) {
    console.log('Updating', userId, karma);
    await User.updateOne(
      { _id: userId },
      { karma },
    );
  }
}

async function backfillInterestNumQuestions() {
  const groups = await Question.aggregate(
    [
      {
        $group: {
          _id: '$interestName',
          numQuestions: {
            $sum: 1,
          },
        },
      },
    ],
  );

  const bulk = Interest.collection.initializeUnorderedBulkOp();
  for await (const group of groups) {
    const filter = { name: group._id };
    const updates = { $set: { numQuestions: group.numQuestions } };
    console.log(filter, updates);
    bulk.find(filter).update(updates);
  }
  const res = await bulk.execute();
  console.log(res);
}

async function backfillInterestNumQuestionsPerLanguage() {
  const groups = await Question.aggregate(
    [
      {
        $group: {
          _id: {
            interestName: '$interestName',
            language: '$language'
          },
          numQuestions: {
            $sum: 1,
          },
        },
      },
    ],
  );

  const interests = {};
  for await (const group of groups) {
    if (!interests[group._id.interestName]) {
      interests[group._id.interestName] = {};
    }
    interests[group._id.interestName][group._id.language] = group.numQuestions;
  }

  const bulk = Interest.collection.initializeUnorderedBulkOp();
  for (const [name, numQuestionsPerLanguage] of Object.entries(interests)) {
    const filter = { name };
    const updates = { $set: { numQuestionsPerLanguage } };
    console.log(filter, updates);
    bulk.find(filter).update(updates);
  }
  const res = await bulk.execute();
  console.log(res);
}

async function addNumQuestionsToInterest(interest) {
  const questions = await Question.find({ interestName: interest.name }, { language: 1 });
  const numQuestions = questions.length;

  const numQuestionsPerLanguage = {};
  for (const question of questions) {
    const lang = question.language;
    if (lang) {
      numQuestionsPerLanguage[lang] = (numQuestionsPerLanguage[lang] || 0) + 1;
    }
  }

  interest.numQuestions = numQuestions;
  interest.numQuestionsPerLanguage = numQuestionsPerLanguage;
  await interest.save();
  console.log(`Updated interest '${interest.name}': numQuestions = ${numQuestions}, languages = [${Object.keys(numQuestionsPerLanguage).join(', ')}]`);
}

async function incrementNumComments(model, scoreFn, postId, userThatCommentedId, postAnonymously = false) {
  const incrementField = postAnonymously ? 'numAnonymousComments' : 'numComments'
  if (model?.modelName == 'Question') {
    await model.updateOne(
      { _id: postId },
      {
        $set: { lastCommentAddedTime : new Date() },
        $inc: { [incrementField]: 1 }
      },
    );
  } else {
    await model.updateOne(
      { _id: postId },
      { $inc: { [incrementField]: 1 } },
    );
  }

  const commentedField = postAnonymously ? 'anonymousUsersThatCommented':'usersThatCommented'
  const res = await model.updateOne(
    {
      _id: postId,
      createdBy: { $ne: userThatCommentedId },
      [commentedField]: { $ne: userThatCommentedId },
    },
    {
      $inc: { numUsersThatCommented: 1 },
      $push: { [commentedField]: userThatCommentedId },
    },
  );

  if (res.modifiedCount) {
    await scoreFn(postId);

    const post = await model.findOne({ _id: postId }, 'createdBy numUsersThatCommented interestName language');
    if (post) {
      await incrementUserKarma(
        post.createdBy,
        post.numUsersThatCommented,
        post.numUsersThatCommented - 1,
        userThatCommentedId,
      );
      await incrementInterestPoints(post.createdBy, post.interestName, post.language, 1);
    }
  }
}

async function likePost(model, scoreFn, user, postId, notificationFn) {
  const userId = user._id;

  const post = await model.findOne({ _id: postId });
  if (!post) {
    return;
  }

  const blocked = await Block.isEitherBlocked(userId, post.createdBy);
  if (blocked) {
    return;
  }

  const res = await model.updateOne(
    {
      _id: postId,
      createdBy: { $ne: userId },
      usersThatLiked: { $ne: userId },
    },
    {
      $inc: { numLikes: 1 },
      $push: { usersThatLiked: userId },
    },
  );

  if (!res.modifiedCount) {
    return;
  }

  await scoreFn(postId);

  post.numLikes++;

  if (model === Question) {
    const question = post;
    const hour = moment().diff(question.createdAt, 'hours');
    if (hour >= 0 && hour <= 23) {
      question.hourlyEngagement[hour].numLikes += 1;
      await question.save();
    }
  }

  await incrementUserKarma(
    post.createdBy,
    post.numLikes,
    post.numLikes - 1,
    userId,
  );

  await incrementInterestPoints(post.createdBy, post.interestName, post.language, 1);

  await user.resetCurrentDayMetricsIfNeeded();

  if (
    user.versionAtLeast('1.11.73')
    && post.numLikes == 1
    && !post.firstLikeAwarded
    && post.createdBy
    && user.currentDayMetrics.karmaFromLikingPosts < 20
  ) {
    // karma award for first like
    await incrementKarma(userId, 1, post.createdBy);

    user.metrics.karmaFromLikingPosts += 1;
    user.currentDayMetrics.karmaFromLikingPosts += 1;
    await user.save();

    post.firstLikeAwarded = true;
    await post.save();
  }

  {
    await User.updateOne(
      { _id: userId },
      { $inc: { 'metrics.numPostLikesSent': 1 } },
    );

    // grant tier rewards to user
    const byUser = await User.findOne({ _id: userId });
    if (byUser) {
      socketLib.sendCoinRewards(
        byUser._id,
        await onGiveLikes(byUser),
      );
    }
  }

  {
    await User.updateOne(
      { _id: post.createdBy },
      { $inc: { 'metrics.numPostLikesReceived': 1 } },
    );

    // grant tier rewards to recipient
    const toUser = await User.findOne({ _id: post.createdBy });
    if (toUser) {
      socketLib.sendCoinRewards(
        toUser._id,
        await onGetLikes(toUser),
      );
    }
  }

  if (!user.shadowBanned) {
    // check if sending notification is needed
    const res = await model.updateOne(
      {
        _id: postId,
        usersThatLikedNotified: { $ne: userId },
      },
      {
        $push: { usersThatLikedNotified: userId },
      },
    );

    const post = await model.findOne({ _id: postId }).populate('createdBy');
    if (res.modifiedCount && post.createdBy && post.createdBy._id != userId) {
      await notificationFn(post);
    }
  }
}

async function unlikePost(model, scoreFn, userId, postId) {
  const res = await model.updateOne(
    {
      _id: postId,
      createdBy: { $ne: userId },
      usersThatLiked: userId,
    },
    {
      $inc: { numLikes: -1 },
      $pull: { usersThatLiked: userId },
    },
  );

  if (!res.modifiedCount) {
    return;
  }

  await scoreFn(postId);

  const post = await model.findOne({ _id: postId }, 'createdBy numLikes interestName language');
  if (post) {
    await incrementUserKarma(
      post.createdBy,
      post.numLikes,
      post.numLikes + 1,
      userId,
    );

    await incrementInterestPoints(post.createdBy, post.interestName, post.language, -1);

    await User.updateOne(
      { _id: userId },
      { $inc: {
        'metrics.numPostLikesSent': -1,
        'metrics.numPostUnlikesSent': 1,
      } },
    );
    await User.updateOne(
      { _id: post.createdBy },
      { $inc: { 'metrics.numPostLikesReceived': -1 } },
    );
  }
}

async function getAwardSenders(req, res, next) {
  const { user } = req;
  const { postId } = req.query;
  const { awardId } = req.query;

  // validate input
  if (!postId || !mongoose.isValidObjectId(postId)) {
    return next(invalidInputError());
  }
  if (awardId) {
    if (typeof awardId !== 'string') {
      return next(invalidInputError());
    }
    const award = coinsConstants.awardsMap[awardId];
    if (!award) {
      return next(notFoundError());
    }
  }

  const query = {
    post: postId,
    recipient: user._id,
    anonymous: false,
  };
  if (awardId) {
    query.award = awardId;
  }

  const awards = await Award
    .find(query)
    .sort('-createdAt')
    .populate('sender');

  const senders = awards.map((award) => ({
    profile: formatProfile(award.sender, req.user),
    awardId: award.award,
  }));

  res.json({
    senders,
  });
}

async function awardPost(req, res, next, postType) {
  const { user } = req;
  const { postId } = req.body;
  const { awardId } = req.body;

  // check input
  if (postType != 'question' && postType != 'comment') {
    return next(applicationError());
  }
  const model = postType == 'question' ? Question : Comment;

  if (!postId || !mongoose.isValidObjectId(postId)) {
    return next(invalidInputError());
  }
  if (!awardId || typeof awardId !== 'string') {
    return next(invalidInputError());
  }

  const award = coinsConstants.awardsMap[awardId];
  if (!award) {
    return next(notFoundError());
  }

  let anonymous = true;
  if (req.body.anonymous == false) {
    anonymous = false;
  }

  // check price
  if (req.body.price != award.price) {
    return next(conflictError('The price of this award has changed.'));
  }

  // check coin balance
  const userMetadata = await UserMetadata.findOne({ user: user._id });
  if (userMetadata.coins < award.price) {
    return next(forbiddenError('Insufficient coins'));
  }

  // award the post
  let result = await model.updateOne(
    {
      _id: postId,
      createdBy: { $ne: user._id },
    },
    {
      $inc: { [`awards.${awardId}`]: 1 },
    },
  );
  if (!result.modifiedCount) {
    return next(notFoundError());
  }
  const post = await model.findOne({ _id: postId });

  // deduct coins from sender
  await coinsLib.updateCoins(
    { user: user._id },
    { $inc: { coins: -1 * award.price } },
    `purchased post award: ${awardId}`,
  );

  // update sender metrics
  user.metrics.numAwardsSent += 1;
  await user.save();

  // grant tier rewards to sender
  socketLib.sendCoinRewards(
    user._id,
    await onGiveAwards(user),
  );

  // update recipient metrics
  result = await User.updateOne(
    {
      _id: post.createdBy,
    },
    {
      $inc: {
        [`awards.${awardId}`]: 1,
        'metrics.numAwardsReceived': 1,
      },
    },
  );
  if (!result.modifiedCount) {
    return res.json({});
  }
  const recipient = await User.findOne({ _id: post.createdBy });

  // give coins to recipient
  await coinsLib.updateCoins(
    { user: recipient._id },
    { $inc: { coins: award.reward } },
    `received post award: ${awardId}`,
  );

  // for ignite award, recalculate score
  if (awardId == 'ignite') {
    const scoreFn = postType == 'question' ? updateQuestionScore : updateCommentScore;
    await scoreFn(postId);
  }

  // grant tier rewards to recipient
  socketLib.sendCoinRewards(
    recipient._id,
    await onGetAwards(recipient),
  );

  // save award
  const awardDoc = new Award({
    sender: user._id,
    recipient: recipient._id,
    post: postId,
    postType,
    award: awardId,
    anonymous,
  });
  await awardDoc.save();

  if (coinsConstants.awardsBatch3.find(x => x.id == awardId) && !recipient.versionAtLeast('1.13.34')) {
    return res.json({});
  }

  const data = {};
  const awardData = { id: awardId };
  data.award = JSON.stringify(awardData);
  if (postType == 'question') {
    const questionData = {
      _id: post._id,
    };
    data.question = JSON.stringify(questionData);
  } else {
    const commentData = {
      _id: post._id,
      question: post.question,
      parent: post.parent,
      postRepliedTo: post.postRepliedTo,
    };
    data.comment = JSON.stringify(commentData);
  }
  if (!anonymous) {
    data.senderId = user._id;
  }

  let notificationBody = '';
  if (!user.versionAtLeast('1.11.0')) {
    notificationBody = '[Update app to latest version to view]';
  }

  // notify recipient
  admin.sendNotification(
    recipient,
    null,
    translate('You got an award!', recipient.locale),
    notificationBody,
    data,
    null,
    'social',
    'received-award-on-post',
  );

  return res.json({});
}

async function reportPost(model, postId, userId) {
  const res = await model.updateOne(
    {
      _id: postId,
      banned: { $ne: true },
      usersThatReported: { $ne: userId },
      interestName: { $ne: 'questions' },
    },
    {
      $push: { usersThatReported: userId },
      $set: { flagged: true },
    },
  );
  if (!res.modifiedCount) {
    return;
  }

  {
    const MetricName = (model === Question) ? 'IncomingReportedPosts' : 'IncomingReportedComments';
    const params = {
      MetricData: [
        {
          MetricName,
          Value: 1,
          Unit: 'Count',
        },
      ],
      Namespace: `SupportTeamMetrics_${process.env.NODE_ENV}`,
    };
    await cloudwatch.putMetricData(params).promise();
  }

  const post = await model.findOne({ _id: postId });
  if (post && (post.usersThatReported.length >= constants.REPORTS_UNTIL_DELETION) && (post.usersThatReported.length / post.numLikes > 0.2)) {
    await banPost(model, postId);
  }
}

async function banPost(model, postId, bannedBy) {
  const res = await model.updateOne(
    {
      _id: postId,
      banned: { $ne: true },
    },
    {
      $set: {
        banned: true,
        bannedDate: Date.now(),
        bannedBy,
        flagged: false,
      },
    },
  );

  if (res.modifiedCount) {
    const post = await model.findOne({ _id: postId }).populate('usersThatReported');

    let reports = [];
    if (model === Question) {
      reports = await PostReport.find({reportedQuestion: postId}).populate('reportedBy');
    } else {
      reports = await PostReport.find({reportedComment: postId}).populate('reportedBy');
    }

    let usersThatReported = post.usersThatReported.concat(reports.map(x => x.reportedBy));

    // award coins to users who sent a report
    for (const user of usersThatReported) {
      if (user) {
        const coins = await coinsLib.updateCoins(
          { user: user._id },
          { $inc: { coins: 50 } },
          'successful report',
        );
        const coinReward = [{
          caption: translate('Successful User Report', user.locale),
          rewardAmount: 50,
          newTotal: coins,
        }];
        socketLib.sendCoinRewards(user._id, coinReward);
        admin.sendNotification(
          user,
          null,
          translate('Thank You', user.locale),
          translate('You’ve been awarded for your successful report and keeping the community safe.', user.locale),
          null,
          null,
          'general',
          'successful-report',
        );
      }
    }

    // update usersWithBannedComments
    if (post.question) {
      await Question.updateOne(
        {
          _id: post.question,
          usersWithBannedComments: { $ne: post.createdBy },
        },
        {
          $push: { usersWithBannedComments: post.createdBy },
        },
      );
    }
  } else {
    // post might already be banned, so clear flagged
    await model.updateOne(
      { _id: postId },
      { flagged: false },
    );
  }
}

async function dismissPost(model, postId) {
  await model.updateOne(
    {
      _id: postId,
    },
    {
      flagged: false,
      usersThatReported: [],
    },
  );
}

function getBeforeDate(date) {
  if (!date || date > Date.now()) {
    return undefined;
  }
  return date;
}
async function parseSortAndPagingParams(req, model, options) {
  if (!options) { options = {}; }

  let sort = RECENT;
  let reverse = false;
  if ([POPULAR, NEARBY].includes(req.query.sort)) {
    sort = req.query.sort;
  } else if (options.allowTop && [TOP_ALL_TIME, TOP_YEAR, TOP_MONTH, TOP_WEEK].includes(req.query.sort)) {
    sort = req.query.sort;
  } else if (options.allowRising && req.query.sort == RISING) {
    sort = req.query.sort;
    req.query.afterId = null;
  } else if (req.query.sort == 'oldest') {
    reverse = true;
  }

  let before = getBeforeDate(req.query.before);

  if (req.query.afterId) { reverse = true; }
  if (sort == RISING) {
    reverse = false;
  }

  const postId = req.query.afterId || req.query.beforeId;
  let beforePost;
  if (postId && mongoose.isValidObjectId(postId)) {
    beforePost = await model.findById(postId).populate('createdBy');
    if (beforePost) {
      if (sort == RECENT || sort == NEARBY) {
        before = beforePost.createdAt;
      } else if (sort == POPULAR || sort == RISING) {
        before = beforePost.score;
      } else if (sort == 'popular_no_image_multiplier') {
        before = beforePost.scoreNoImageMultiplier;
      } else if (sort == TOP_ALL_TIME) {
        before = beforePost.nonDecayedScore;
      } else if (sort == TOP_YEAR) {
        before = beforePost.scoreYear;
      } else if (sort == TOP_MONTH) {
        before = beforePost.scoreMonth;
      } else if (sort == TOP_WEEK) {
        before = beforePost.scoreWeek;
      }

      if (sort == NEARBY
        && beforePost.createdBy
        && !isLocal(req.user, beforePost.createdBy)) {
        sort = NOT_NEARBY;
      }
    }
  }

  return {
    sort,
    before,
    reverse,
    beforePost,
  };
}

function addLanguageFilter(user, language, matchBy) {
  let languageArray = [];
  if (language == 'he') {
    languageArray.push('iw');
  } else if (language && languageCodes.includes(language)) {
    languageArray.push(language);
  } else if (user && user.languages.length > 0) {
    languageArray = user.languages;
  }

  if (languageArray.length == 0) {
    languageArray.push('en');
  }

  if (languageArray.length == 1) {
    matchBy.language = languageArray[0];
  } else if (languageArray.length > 1) {
    matchBy.language = { $in: languageArray };
  }
}

function addTextSearchFilter(toSearch, matchBy) {
  if (!toSearch) {
    return;
  }

  if (typeof toSearch !== 'string' || toSearch.length > 1000) {
    throw invalidInputError('search must be string of maximum 1000 characters');
  }

  const words = extractValidWords(toSearch);

  if (words.length > 0) {
    matchBy.keywords = { $all: words };
  }
}

async function updateNotification(user, post, postType, notificationType, data, profile, numProfiles) {
  let notification = await Notification.findOne({
    user,
    post: post._id,
    notificationType,
  });
  if (!notification) {
    notification = new Notification({
      user: user,
      post: post._id,
      postType,
      notificationType,
    });
  }
  notification.updatedAt = Date.now();
  notification.seen = false;
  notification.data = data;
  notification.profile = profile;
  notification.numProfiles = numProfiles;
  await notification.save();
}
async function markNotificationSeen(user, post, notificationType) {
  if (!user || !post || !notificationType) {
    return;
  }
  const notification = await Notification.findOne({
    post,
    notificationType,
  });
  if (!notification || notification.user != user) {
    return;
  }
  notification.seen = true;
  await notification.save();
}

function generateInterestObjectForSitemap(interest) {
  let interestName = interest.name
  const lastModified = interest.lastPostAddedTime || interest.createdAt
  interestName = encodeURIComponent(interestName);
  const url = `${WEB_DOMAIN}/u/${interestName}`;
  return {
    url,
    lastModified,
  };
}

function generateInterestUrl(interestName, language) {
  interestName = encodeURIComponent(interestName);
  let url = `${WEB_DOMAIN}/u/${interestName}`;
  if (language && language != 'en') {
    if (language == 'iw') language = 'he'
    if (language == 'zh') language = 'zh-Hans'
    if(locales.includes(language)) url = `${WEB_DOMAIN}/${language}/u/${interestName}`;
  }
  return url;
}

function generateQuestionUrl(question, includeLanguage) {
  let slug = createSlug(question.title);
  if (slug.length < 20) {
    slug = createSlug(`${slug} ${question.text}`);
  }
  let language = includeLanguage ? question.language : undefined;
  let url = `${generateInterestUrl(question.interestName, language)}/${question.webId}/${encodeURIComponent(slug)}`;
  // Remove trailing slashes
  url = url.replace(/\/+$/, '');
  return url;
}

function formatQuestion(question, requestingUser) {
  let poll;
  if (question.poll
    && question.poll.options
    && question.poll.options.length
  ) {
    poll = {
      options: question.poll.options,
      optionVotedByUser: question.poll.optionVotedByUser,
    };
  }

  let { createdBy } = question;
  if (question.interestName == 'questions' && requestingUser && !requestingUser.versionAtLeast('1.11.47')) {
    createdBy = null;
  }
  if (createdBy && createdBy.shadowBanned && (!requestingUser || requestingUser._id != createdBy._id)) {
    createdBy = null;
  }
  if (question.block && question.block.length > 0) {
    createdBy = null;
  }

  let isBoosted;
  if (requestingUser && requestingUser.versionAtLeast('1.11.61')) {
    isBoosted = question.isBoosted || false;
  }

  let image = question.image;
  if (image) {
    if (requestingUser && !requestingUser.versionAtLeast('1.12.2')) {
      // filter out videos for old versions
      const videoExtensions = ['.mp4', '.webm', '.avi', '.flv', '.mkv', '.mpg', '.wmv', '.mov'];
      if (videoExtensions.some((ext) => image.includes(ext))) {
        image = undefined;
      }
    } else if (question.convertedVideo == image) {
      /*
      const key = image;
      const base = key.split('/').pop().split('.')[0];
      image = `${key}/${base}.m3u8`;
      */
    }
  }

  let altText = question.altText

  let images = question.images;
  if (Array.isArray(images) && images.length > 0) {
    if (requestingUser && !requestingUser.versionAtLeast('1.13.68')) {
      image = images[0].image
      altText = images[0].altText
    }
  }

  let videoThumbnail;
  if (question.convertedVideo) {
    const key = question.convertedVideo;
    const base = key.split('/').pop().split('.')[0];
    videoThumbnail = `${key}/${base}-thumb.0000000.jpg`;
  }

  let formattedCreatedBy, profilePreview, allowIncomingRequests, postedAnonymouslyBy;
  if(question.postedAnonymously && requestingUser.versionAtLeast('1.13.74')){
    postedAnonymouslyBy = formatPostedAnonymouslyBy(createdBy)
  }else if (useProfilePreview(requestingUser)) {
    profilePreview = formatProfilePreview(createdBy, requestingUser);
  } else {
    formattedCreatedBy = formatProfile(createdBy, requestingUser, { nearby: true, isUniverse: true });
  }
  if (requestingUser && requestingUser.versionAtLeast('1.13.12')) {
    allowIncomingRequests = genderPreferenceLib.isIncomingRequestAllowed(createdBy, requestingUser);
  }

  let createdByInterestRank;
  if (question.createdBy && question.createdBy.interestPoints && requestingUser && requestingUser.versionAtLeast('1.13.35')) {
    let found = question.createdBy.interestPoints.find(x => x.interest == question.interestName && x.language == question.language);
    if (found && found.rank) {
      createdByInterestRank = found.rank;
    }
  }

  let numComments = question.numComments
  // temporarily remove anonymous feature
  /*
  if(requestingUser && requestingUser.versionAtLeast('1.13.74') && isAppUser(requestingUser) && question.numAnonymousComments) {
    numComments = question.numComments + question.numAnonymousComments
  }
  */

  const locale = requestingUser?.locale || 'en';

  let { hashtags, interest, interestName, parent, interestMergeChecked } = question;

  if (interestMergeChecked) {
    interest = interestLib.formatInterest(parent?.toString(), locale, true);
    interestName = interest?.name || question.interestName;
    hashtags = interestLib.getTranslatedInterestsFromKeys(hashtags, locale).map(x => x.name);
  } else {
    interest = interestLib.formatInterest(parent?.toString(), locale);
  }

  return {
    _id: question._id,
    webId: question.webId,
    createdAt: question.createdAt,
    createdBy: formattedCreatedBy,
    profilePreview,
    postedAnonymouslyBy,
    allowIncomingRequests,
    title: question.title,
    text: question.text,
    image: image,
    images,
    videoThumbnail: videoThumbnail,
    aspectRatio: question.aspectRatio,
    altText,
    audio: question.audio,
    audioWaveform: question.audioWaveform,
    audioDuration: question.audioDuration,
    gif: processGifUrlForDownsize(question.gif),
    interest,
    interestName,
    numComments,
    numLikes: question.numLikes,
    numViews: requestingUser && requestingUser.versionAtLeast('1.11.37') ? question.numViews : undefined,
    isDeleted: question.isDeleted,
    isEdited: question.isEdited,
    hasUserLiked: question.hasUserLiked || false,
    hasUserSaved: question.hasUserSaved || false,
    language: question.language ? question.language : 'en',
    url: generateQuestionUrl(question),
    awards: question.awards,
    poll,
    friendsThatCommented: question.friendsThatCommented,
    isBoosted,
    createdByInterestRank,
    linkedKeywords: question.linkedKeywords,
    linkedExploreKeywords: question.linkedExploreKeywords,
    linkedPillarKeywords: question.linkedPillarKeywords,
    linkedCategories: question.linkedCategories,
    linkedSubcategories: question.linkedSubcategories,
    linkedProfiles: question.linkedProfiles,
    mentionedUsersTitle: question.mentionedUsersTitle,
    mentionedUsersText: question.mentionedUsersText,
    hashtags,
  };
}

function formatPostedAnonymouslyBy(user){
  return {
    anonymousProfileNickname: user.anonymousProfileNickname,
    personality: {
      mbti: user.personality?.mbti,
      ...(user.personality?.mbti && {avatar: personalityLib.getAvatar(user.personality.mbti, user.locale).avatar})
    },
    enneagram: user.enneagram,
    horoscope: user.horoscope,
    gender: user.gender,
    age: user.age
  };
}

async function getQuestions(user, sortCriteria, reverse, originalMatchBy, before, pageSize, lookupQuestion, showAll, applyPreferenceFilters, isFriendsFeed, videosOnly, countryCode, filter, multipliers, atlasSearchQuery) {
  const questions = await getPosts(user, sortCriteria, reverse, originalMatchBy, before, pageSize, Question, formatQuestion, lookupQuestion, showAll, applyPreferenceFilters, isFriendsFeed, null, videosOnly, countryCode, filter, multipliers, atlasSearchQuery);

  /*
  // update numViews in database
  if (user && user._id) {
    Question.incrementViews(questions.posts.map((posts) => {
      return posts._id;
    }), user._id);
  }
  */

  return questions;
}

function formatComment(comment, requestingUser) {
  let createdBy, profilePreview, allowIncomingRequests, postedAnonymouslyBy, repliedTo, repliedToAnonymous;

  if(comment.postedAnonymously && requestingUser.versionAtLeast('1.13.74')){
    postedAnonymouslyBy = formatPostedAnonymouslyBy(comment.createdBy)
  }else if (useProfilePreview(requestingUser)) {
    profilePreview = formatProfilePreview(comment.createdBy, requestingUser);
  } else {
    createdBy = formatProfile(comment.createdBy, requestingUser, { nearby: true, isUniverse: true });
  }
  if (requestingUser && requestingUser.versionAtLeast('1.13.12')) {
    allowIncomingRequests = genderPreferenceLib.isIncomingRequestAllowed(comment.createdBy, requestingUser);
  }
  let image = comment.image;
  if (image) {
    if (!requestingUser || !requestingUser.versionAtLeast('1.13.24')) {
      // filter out videos for old versions
      const videoExtensions = ['.mp4', '.webm', '.avi', '.flv', '.mkv', '.mpg', '.wmv', '.mov'];
      if (videoExtensions.some((ext) => image.includes(ext))) {
        image = undefined;
      }
    }
  }

  let createdByInterestRank;
  if (comment.createdBy && comment.createdBy.interestPoints && requestingUser && requestingUser.versionAtLeast('1.13.35')) {
    let found = comment.createdBy.interestPoints.find(x => x.interest == comment.interestName && x.language == comment.language);
    if (found && found.rank) {
      createdByInterestRank = found.rank;
    }
  }
  let numComments = comment.numComments

  if(requestingUser && requestingUser.versionAtLeast('1.13.74') && isAppUser(requestingUser) && comment.numAnonymousComments) {
    numComments = comment.numComments + comment.numAnonymousComments
  }

  if(comment.repliedToIsAnonymous && comment.repliedTo){
    repliedToAnonymous = formatPostedAnonymouslyBy(comment.repliedTo)
  }else{
    repliedTo = formatProfile(comment.repliedTo, requestingUser)
  }

  const locale = requestingUser?.locale || 'en';
  let { interestName, interestMergeChecked } = comment;

  if (interestMergeChecked) {
    interestName = interestLib.getTranslatedInterestsFromKeys([interestName], locale)?.[0]?.name;
  }

  return {
    _id: comment._id,
    createdAt: comment.createdAt,
    createdBy,
    profilePreview,
    postedAnonymouslyBy,
    allowIncomingRequests,
    question: comment.question,
    interestName,
    profile: comment.profile,
    questionUrl: comment.populatedQuestion ? generateQuestionUrl(comment.populatedQuestion) : undefined,
    text: comment.text,
    image,
    aspectRatio: comment.aspectRatio,
    audio: comment.audio,
    audioWaveform: comment.audioWaveform,
    audioDuration: comment.audioDuration,
    gif: processGifUrlForDownsize(comment.gif),
    parent: comment.parent,
    repliedTo,
    repliedToAnonymous,
    depth: comment.depth,
    numComments,
    numLikes: comment.numLikes,
    isDeleted: comment.isDeleted,
    isEdited: comment.isEdited,
    hasUserLiked: comment.hasUserLiked || false,
    comments: comment.comments || [],
    postRepliedTo: comment.postRepliedTo,
    awards: comment.awards,
    vote: comment.vote,
    isFriendComment: comment.postedAnonymously ? false : comment.isFriendComment || false,
    linkedKeywords: comment.linkedKeywords,
    linkedExploreKeywords: comment.linkedExploreKeywords,
    linkedPillarKeywords: comment.linkedPillarKeywords,
    linkedCategories: comment.linkedCategories,
    linkedSubcategories: comment.linkedSubcategories,
    linkedProfiles: comment.linkedProfiles,
    mentionedUsersText: comment.mentionedUsersText,
    createdByInterestRank,
    language: comment.language,
  };
}

async function getCommentPage(user, parentId, commentId, before, sort, reverse, pageSize, showFriendComments) {
  const matchBy = { parent: formatId(parentId) };
  if (commentId) {
    matchBy._id = formatId(commentId);
  }

  let comments = await getPosts(
    user,
    sort,
    reverse,
    matchBy,
    before,
    pageSize,
    Comment,
    formatComment,
    null,
    null,
    null,
    null,
    showFriendComments,
  );

  if (sort == NEARBY && user && user.location && comments.length < pageSize) {
    const moreComments = await getPosts(
      user,
      NOT_NEARBY,
      reverse,
      matchBy,
      null,
      pageSize - comments.length,
      Comment,
      formatComment,
      null,
    );
    comments = comments.concat(moreComments);
  }

  if (!user || user.versionAtLeast('1.13.12')) {
    return comments;
  }

  const parentIds = comments.map((c) => formatId(c._id));
  const childComments = await getPosts(
    user,
    RECENT,
    true,
    { parent: { $in: parentIds } },
    null,
    null,
    Comment,
    formatComment,
  );
  childComments.reverse();

  const start = new Date().getTime();
  for (const comment of childComments) {
    const i = comments.findIndex((c) => c._id.equals(comment.parent));
    if (i >= 0) {
      comments[i].comments.push(comment);
    }
  }
  const end = new Date().getTime();
  console.log(`User ${user?._id} Time to process child comments: ${end-start} ms. Child comments length: ${childComments.length}`);

  return comments;
}

// route handlers
async function getQuestion(req, res, next) {
  if (req.user) {
    if (req.headers['from-web']) {
      req.user.os = 'web';
    } else if (req.headers.from) {
      req.user.os = req.headers.from;
    }
  }

  if (!req.query.questionId && !req.query.webId) {
    return next(notFoundError());
  }
  if (req.query.questionId && !mongoose.isValidObjectId(req.query.questionId)) {
    return next(invalidInputError());
  }

  let locale = req.user?.locale || 'en';
  locale = req.query?.language || locale;
  let interestName = 'questions';

  if (req.query?.interestName) {
    if (locale !== 'en') {
      interestName = await interestLib.getInterestKeyFromTranslation(req.query.interestName, locale);
      if (!interestName) return next(notFoundError());
    } else {
      interestName = req.query.interestName;
    }
  } else if (req.query?.interestId) {
    const translatedInterest = interestLib.translatedInterests?.[req.query.interestId];
    if (!translatedInterest) return next(notFoundError());
    interestName = translatedInterest.name;
  }

  if (req.query.webId && typeof req.query.webId !== 'string') {
    return next(notFoundError());
  }

  const matchBy = {};

  if (req.query.webId) {
    matchBy.webId = req.query.webId;
    matchBy.interestName = interestName;
  }
  if (req.query.questionId) {
    matchBy._id = formatId(req.query.questionId);
  }

  const questions = await getQuestions(
    req.user,
    RECENT,
    false,
    matchBy,
    null,
    1,
  );

  if (questions.length == 0) {
    return next(notFoundError(req.__('This post has been deleted.')));
  }
  const question = questions[0];

  if (req.user && !req.user.versionAtLeast('1.13.22')) {
    await markNotificationSeen(req.uid, question._id, 'like');
  }

  return res.json({
    question,
  });
}

async function getQuestionInternalLinking(req, res, next) {
  try {
    const { questionId } = req.query;
    if (!questionId) {
      return next(notFoundError());
    }
    if (!mongoose.isValidObjectId(questionId)) {
      return next(invalidInputError());
    }
    const question = await Question.findById(questionId).select('interestName language createdAt');
    if (!question) {
      return next(notFoundError());
    }
    const matchBy = {
      createdAt: { $lt: question.createdAt },
      interestName: question.interestName,
      language: question.language
    };
    const questions = await getQuestions(
      null,
      RECENT,
      false,
      matchBy,
      null,
      1.5, // page size (we want 3 posts, but page size is doubled for web)
    );
    return res.json({ questions });
  } catch (error) {
    return next(error);
  }
}

async function getQuestionsBasedOnProfilesName(req, res, next) {
  try {
    const { profileId, language } = req.query;
    if (!profileId) {
      return next(notFoundError());
    }
    const profile = await Profile.findOne({ id : profileId })
    if (!(profile?.name)) {
      return next(notFoundError());
    }
    let atlasSearchQuery =  {
      $search: {
        index: 'question_for_related_profiles',
        compound: {
          must: [
            {
              phrase: {
                path: 'language',
                query: language ? language : 'en',
              },
            },
            {
              text: {
                path: ['name', 'text', 'poll.options.text'],
                query: profile.name,
                matchCriteria: 'all',
                fuzzy: { maxEdits: 1 },
              },
            },
          ],
          mustNot: [
            {
              equals: {
                path: 'mediaUploadPending',
                value: true
              }
            },
            {
              equals: {
                path: 'postedAnonymously',
                value: true
              }
            },
          ]
        },
        sort: {
          numComments: -1,
          createdAt: -1,
        },
      },
    }
    const questions = await getQuestions(null, null, false, {}, null, 7.5, null, null, null, null, null, null, null, null, atlasSearchQuery);
    return res.json({ questions });
  } catch (error) {
    return next(error);
  }
}

async function getQuestionsBasedOnProfilesSubCategory(req, res, next) {
  try {
    const { profileId, language } = req.query;
    if (!profileId) {
      return next(notFoundError());
    }
    const profile = await Profile.findOne({ id : profileId })
    if (!(profile?.subcategories?.length)) {
      return next(notFoundError());
    }
    let atlasSearchQuery =  {
      $search: {
        index: 'question_for_related_profiles',
        compound: {
          must: [
            {
              phrase: {
                path: 'language',
                query: language ? language : 'en',
              },
            },
            {
              in: {
                path: 'linkedSubcategories.id',
                value: profile.subcategories,
              },
            },
          ],
          mustNot: [
            {
              equals: {
                path: 'mediaUploadPending',
                value: true
              }
            },
            {
              equals: {
                path: 'postedAnonymously',
                value: true
              }
            },
          ]
        },
        sort: {
          numComments: -1,
          createdAt: -1,
        },
      },
    }
    const questions = await getQuestions(null, null, false, {}, null, 7.5, null, null, null, null, null, null, null, null, atlasSearchQuery);
    return res.json({ questions });
  } catch (error) {
    return next(error);
  }
}
async function getQuestionFeed(params) {
  let {
    user,
    sort,
    before,
    reverse,
    filter,
    language,
    additionalMatchByFilters,
    search,
    videosOnly,
    countryCode,
    multipliers,
  } = params;

  // explore - all topics except daily questions and hidden interests
  let hiddenInterests = [];
  if (user && Array.isArray(user.hiddenInterests)) {
    hiddenInterests = user.hiddenInterests;
  }
  hiddenInterests.push('questions');
  let matchBy = { interestName: { $nin: hiddenInterests } };

  // following
  if (filter == 'following' && user) {
    const interestNames = arrayDiff(user.interestNames, hiddenInterests);
    const followingUserIds = await Follow.distinct(
      'to',
      { from: user._id },
    );
    const orArr = [];
    if (interestNames && interestNames.length > 0) {
      orArr.push({ interestName: { $in: interestNames } });
    }
    if (followingUserIds && followingUserIds.length > 0) {
      orArr.push({ createdBy: { $in: followingUserIds } });
    }
    if (orArr.length > 0) {
      matchBy = { $or: orArr };
    }
  }

  // friends
  let isFriendsFeed;
  if (filter === 'friends' && user) {
    isFriendsFeed = true;

    const friendIds = await FriendList.getFriendIds(user._id);
    if (!(friendIds || []).length) {
      return [];
    }

    matchBy = { createdBy: { $in: friendIds } };

    if (user.versionAtLeast('1.11.60')) {
      matchBy = {
        $or: [
          { createdBy: { $in: friendIds } },
          { usersThatCommented: { $in: friendIds } },
        ],
      };
    }
  } else {
    addLanguageFilter(user, language, matchBy);
  }

  addTextSearchFilter(search, matchBy);

  if (additionalMatchByFilters) {
    for (const [key, value] of Object.entries(additionalMatchByFilters)) {
      matchBy[key] = value;
    }
  }

  if (user && (filter == 'explore' || filter == 'for_you')) {
    matchBy.$or = [
      {
        faceDetected: { $ne: true },
      },
      {
        faceDetected: true,
        'userAttributes.genderPreferenceHash': { $in: genderPreferenceLib.getCompatibleDatingHashes(user.genderPreferenceHash) }
      },
      {
        createdBy: user._id,
      },
    ];
  }

  if (sort == RECENT && (filter == 'explore' || filter == 'for_you') && language == 'en' && user) {
    const sandboxCountries = ['PH', 'ID', 'IN'];
    if (sandboxCountries.includes(user.countryCode)) {
      matchBy['region'] = getRegion(user.countryCode);
    }
    else {
      matchBy['userAttributes.countryCode'] = { $nin: sandboxCountries };
    }
  }

  let questions = await getQuestions(
    user,
    sort,
    reverse,
    matchBy,
    before,
    constants.getPageSize(),
    null,
    null,
    search ? false : true,
    isFriendsFeed,
    videosOnly,
    countryCode,
    filter,
    multipliers,
  );

  // for first page, pin most recent question of day to front
  if (!before && !sort.includes('top') && (!filter || filter == 'explore' || filter == 'for_you') && !matchBy.keywords && !videosOnly) {
    matchBy = {
      interestName: 'questions',
      createdAt: { $gt: new Date(Date.now() - 24 * 3600 * 1000) },
    };
    addLanguageFilter(user, language, matchBy);
    const qod = await getQuestions(
      user,
      RECENT,
      false,
      matchBy,
      Date.now(),
      constants.getPageSize(),
    );
    questions = qod.concat(questions);
  }

  return questions;
}

async function getQuestionFeedRouteHandler(req, res, next) {
  if (req.user) {
    if (req.headers['from-web']) {
      req.user.os = 'web';
    } else if (req.headers.from) {
      req.user.os = req.headers.from;
    }
  }

  let { sort, before, reverse, beforePost } = await parseSortAndPagingParams(req, Question, { allowTop: true, allowRising: true });

  let filter = req.query.filter;
  if (filter == 'for_you') {
    filter = 'explore';
  }

  const customFeed = req.user?.findCustomFeed(filter);

  let countryCode;
  let multipliers = {};
  if ((!filter || filter == 'explore' || filter == 'for_you' || customFeed?.prioritizeNearby || customFeed?.prioritizeSameCountry) && (sort == POPULAR || sort == 'popular_no_image_multiplier') && !req.query.search) {
    if (req.user) {
      countryCode = req.user.countryCode || req.user.ipData.countryCode;
    }
    if (!countryCode) {
      const geo = geoip.lookup(req.ip);
      if (geo) {
        countryCode = geo.country;
      }
    }

    const region = getRegion(countryCode);
    if (filter == 'for_you') {
      multipliers.prioritizeNearby = true;
      multipliers.prioritizeSameCountry = true;
      multipliers.prioritizeSameRegion = true;
      multipliers.prioritizeSameInterests = true;
    }
    else if (customFeed) {
      multipliers.prioritizeNearby = customFeed.prioritizeNearby;
      multipliers.prioritizeSameCountry = customFeed.prioritizeSameCountry;
      multipliers.prioritizeSameRegion = false;
      multipliers.prioritizeSameInterests = false;
    }
    else if (region) {
      multipliers.prioritizeNearby = true;
      multipliers.prioritizeSameCountry = true;
      multipliers.prioritizeSameRegion = true;
      multipliers.prioritizeSameInterests = false;
    }

    if (beforePost) {
      before = getWeightedScore(beforePost, req.user, sort, countryCode, multipliers);
    }
  }

  if (req.user && req.query.language && req.query.language != req.user.socialFeedLanguage) {
    req.user.socialFeedLanguage = req.query.language;
    await req.user.save();
  }

  const questions = await getQuestionFeed({
    user: req.user,
    sort,
    before,
    reverse,
    filter: filter,
    language: req.query.language,
    search: req.query.search,
    videosOnly: req.query.videosOnly,
    countryCode,
    multipliers,
  });

  res.json({
    questions,
  });
}

async function getQuestionAllQuestions(req, res, next) {
  if (req.user) {
    if (req.headers['from-web']) {
      req.user.os = 'web';
    } else if (req.headers.from) {
      req.user.os = req.headers.from;
    }
  }

  let locale = req.user?.locale || 'en';
  locale = req.query?.language || locale;
  let interestName = 'questions';

  if (req.query?.interestName) {
    if (locale !== 'en') {
      interestName = await interestLib.getInterestKeyFromTranslation(req.query.interestName, locale);
      if (!interestName) return next(notFoundError());
    } else {
      interestName = req.query.interestName;
    }
  } else if (req.query?.interestId) {
    const translatedInterest = interestLib.translatedInterests?.[req.query.interestId];
    if (!translatedInterest) return next(notFoundError());
    interestName = translatedInterest.name;
  }

  const { sort, before, reverse } = await parseSortAndPagingParams(req, Question, { allowTop: true, allowRising: true });

  const matchBy = { hashtags: interestName };
  if (interestName == 'questions') {
    matchBy.createdAt = { $lte: new Date() };
  }
  addLanguageFilter(req.user, req.query.language, matchBy);
  addTextSearchFilter(req.query.search, matchBy);

  const questions = await getQuestions(
    req.user,
    sort,
    reverse,
    matchBy,
    before,
    constants.getPageSize(),
    null,
    null,
    false,
    null,
    req.query.videosOnly,
  );

  res.json({
    questions,
  });
}

async function getSingleCommentById(req, res, next) {
  const { commentId } = req.query;
  if (!commentId || !mongoose.isValidObjectId(commentId)) {
    return next(invalidInputError());
  }

  const comments = await getPosts(
    req.user,
    null,
    null,
    { _id: formatId(commentId) },
    null,
    null,
    Comment,
    formatComment,
  );

  if (comments.length == 0) {
    return next(notFoundError());
  }

  const comment = comments[0];

  if (comment.question) {
    const question = await Question.findOne({_id: comment.question}, 'title text interestName webId').lean();
    if (question) {
      comment.questionUrl = generateQuestionUrl(question);
    }
  }

  return res.json({
    comment,
  });
}

async function getComment(req, res, next) {
  if (!req.query.questionId && !req.query.parentId && !req.query.profileId && !req.query.webPageUrl) {
    return next(invalidInputError('invalid query'));
  }

  let webPageId;
  if (req.query.webPageUrl) {
    const webPage = await WebPage.findOne({ url: req.query.webPageUrl });
    if (!webPage) {
      return next(notFoundError());
    }
    webPageId = webPage._id;
  }

  const { sort, before, reverse } = await parseSortAndPagingParams(req, Comment);

  // check if parentId is a comment which mean retrieve level2 comment / reply
  const isReply = (req.query.parentId && await Comment.findOne({_id: req.query.parentId})) ? true : false
  // friend sort requires a logged-in user, and it is not compatible with nearby sort and is not a level2 comment
  const showFriendCommentsFirst = !isReply && req.user !== undefined && (sort === RECENT || sort === POPULAR);

  let comments = [];

  if (showFriendCommentsFirst) {
    if (!before) {
      // get all friend comments in the first page
      comments = comments.concat(
        await getCommentPage(
          req.user,
          req.query.questionId || req.query.profileId || req.query.parentId || webPageId,
          null,
          before,
          sort,
          reverse,
          null, // no page limit
          true, // get only friend comments
        ),
      );
    }

    // get non-friend comments with a page limit
    comments = comments.concat(
      await getCommentPage(
        req.user,
        req.query.questionId || req.query.profileId || req.query.parentId || webPageId,
        null,
        before,
        sort,
        reverse,
        constants.getPageSize(),
        false, // get only non-friend comments
      ),
    );
  } else {
    comments = comments.concat(
      await getCommentPage(
        req.user,
        req.query.questionId || req.query.profileId || req.query.parentId || webPageId,
        null,
        before,
        sort,
        reverse,
        constants.getPageSize(),
        null, // no friend-related filtering
      ),
    );
  }

  if (req.query.sort == 'oldest') {
    comments.reverse();
  }

  let hasMore;
  if (req.query.sort == 'oldest' && req.query.parentId && comments.length > 0 && req.user && req.user.versionAtLeast('1.13.13')) {
    const nextBefore = comments[comments.length - 1].createdAt;
    const nextComments = await getCommentPage(
      req.user,
      req.query.parentId,
      null,
      nextBefore,
      sort,
      reverse,
      5,
      null, // no friend-related filtering
    );
    hasMore = nextComments.length > 0;
  }

  comments = comments.map(comment => {
    if (comment.vote && Object.keys(comment.vote).length === 0) {
      delete comment.vote;
    }
    return comment;
  });

  res.json({
    comments,
    hasMore,
  });
}

async function getCommentContext(req, res, next) {
  if (
    !req.query.commentId || !mongoose.isValidObjectId(req.query.commentId)
    || !req.query.parentId || !mongoose.isValidObjectId(req.query.parentId)
    || !req.query.questionId || !mongoose.isValidObjectId(req.query.questionId)
  ) {
    return next(invalidInputError());
  }
  const { commentId } = req.query;
  const { parentId } = req.query;
  const { questionId } = req.query;
  const { postRepliedTo } = req.query;

  if (req.user && !req.user.versionAtLeast('1.13.22')) {
    await markNotificationSeen(req.uid, commentId, 'like');
    await markNotificationSeen(req.uid, postRepliedTo, 'reply');
  }

  const highlightedCommentId = parentId == questionId ? commentId : parentId;

  const { sort, before, reverse } = await parseSortAndPagingParams(req, Comment);
  const pageSize = constants.getPageSize();

  const comments = await getCommentPage(
    req.user,
    questionId,
    null,
    null,
    sort,
    reverse,
    pageSize,
  );
  if (!comments.find((x) => x._id == highlightedCommentId)) {
    const highlightedComment = await getCommentPage(
      req.user,
      questionId,
      highlightedCommentId,
      null,
      sort,
      reverse,
      pageSize,
    );
    if (highlightedComment.length > 0) {
      comments.unshift(highlightedComment[0]);
    }
  }

  const user = req.user;
  if (!user || user.versionAtLeast('1.13.12')) {
    if (parentId != questionId) {
      const parentCommentIndex = comments.findIndex((c) => c._id.equals(parentId));
      if (parentCommentIndex >= 0) {
        const childComments = await getCommentPage(
          user,
          parentId,
          null,
          null,
          RECENT,
          true,
          null,
        );
        childComments.reverse();
        comments[parentCommentIndex].comments = childComments;
      }
    }
  }

  return res.json({
    comments,
  });
}

async function getUserQuestions(req, res, next) {
  if (!req.query.createdBy || typeof req.query.createdBy !== 'string') {
    return next(invalidInputError());
  }
  const createdBy = await User.findById(req.query.createdBy);
  if (!createdBy) {
    return next(notFoundError());
  }
  if (createdBy.hideQuestions && createdBy._id != req.uid && !req.user.admin) {
    return res.json({ questions: [] });
  }
  const blocked = await Block.isEitherBlocked(req.uid, req.query.createdBy);
  if (blocked) {
    return res.json({ questions: [] });
  }

  const { sort, before, reverse } = await parseSortAndPagingParams(req, Question);

  const matchBy = { createdBy: req.query.createdBy, postedAnonymously: { $ne: true} };

  const questions = await getQuestions(
    req.user,
    sort,
    reverse,
    matchBy,
    before,
    constants.getPageSize(),
    null,
    req.user.admin,
  );

  res.json({
    questions,
  });
}

async function getUserComments(req, res, next) {
  if (!req.query.createdBy || typeof req.query.createdBy !== 'string') {
    return next(invalidInputError());
  }
  const createdBy = await User.findById(req.query.createdBy);
  if (!createdBy) {
    return next(notFoundError());
  }
  if (createdBy.hideComments && createdBy._id != req.uid && !req.user.admin) {
    return res.json({ comments: [] });
  }
  const blocked = await Block.isEitherBlocked(req.uid, req.query.createdBy);
  if (blocked) {
    return res.json({ comments: [] });
  }

  const { sort, before, reverse } = await parseSortAndPagingParams(req, Comment);

  const matchBy = { createdBy: req.query.createdBy, postedAnonymously: { $ne: true} };

  const comments = await getPosts(
    req.user,
    sort,
    reverse,
    matchBy,
    before,
    constants.getPageSize(),
    Comment,
    formatComment,
    true, // lookupQuestion: only returns comments on questions (not profiles)
    req.user.admin,
  );

  res.json({
    comments,
  });
}

async function getUserAnonymousQuestions(req, res, next) {
  if((!req.user.versionAtLeast('1.13.74') || !isAppUser(req.user))) return next(invalidInputError('get anonymous post not allowed'));
  if (!req.query.anonymousProfileNickname || typeof req.query.anonymousProfileNickname !== 'string') {
    return next(invalidInputError());
  }

  const createdBy = await User.findOne({anonymousProfileNickname: req.query.anonymousProfileNickname})
  if (!createdBy) {
    return next(notFoundError());
  }
  if (createdBy.hideQuestions && createdBy._id != req.uid && !req.user.admin) {
    return res.json({ questions: [] });
  }
  const blocked = await Block.isEitherBlocked(req.uid, req.query.createdBy);
  if (blocked) {
    return res.json({ questions: [] });
  }

  const { sort, before, reverse } = await parseSortAndPagingParams(req, Question);

  const matchBy = { createdBy: createdBy._id, postedAnonymously: true };

  const questions = await getQuestions(
    req.user,
    sort,
    reverse,
    matchBy,
    before,
    constants.getPageSize(),
    null,
    req.user.admin,
  );

  res.json({
    questions,
  });
}

async function getUserAnonymousComments(req, res, next) {
  if((!req.user.versionAtLeast('1.13.74') || !isAppUser(req.user))) return next(invalidInputError('get anonymous comments not allowed'));
  if (!req.query.anonymousProfileNickname || typeof req.query.anonymousProfileNickname !== 'string') {
    return next(invalidInputError());
  }

  const createdBy = await User.findOne({anonymousProfileNickname: req.query.anonymousProfileNickname})
  if (!createdBy) {
    return next(notFoundError());
  }
  if (createdBy.hideComments && createdBy._id != req.uid && !req.user.admin) {
    return res.json({ comments: [] });
  }
  const blocked = await Block.isEitherBlocked(req.uid, req.query.createdBy);
  if (blocked) {
    return res.json({ comments: [] });
  }

  const { sort, before, reverse } = await parseSortAndPagingParams(req, Comment);

  const matchBy = { createdBy: createdBy._id, postedAnonymously: true };

  const comments = await getPosts(
    req.user,
    sort,
    reverse,
    matchBy,
    before,
    constants.getPageSize(),
    Comment,
    formatComment,
    true, // lookupQuestion: only returns comments on questions (not profiles)
    req.user.admin,
  );

  res.json({
    comments,
  });
}

async function createQuestion(params) {
  if (!params.hashtags) {
    params.hashtags = [params.interestName];
  }
  let question;
  for (let numTries = 0; numTries < 10; numTries++) {
    params.webId = await webIdLib.getWebId();
    const newQuestion = new Question(params);
    try {
      question = await newQuestion.save();
      break;
    } catch (err) {
      console.log(err);
    }
  }
  if (question) {
    let updatedInterest = await Interest.findOneAndUpdate(
      { name: question.interestName },
      {
        $set: { lastPostAddedTime: new Date() },
        $inc: { numQuestions: 1, [`numQuestionsPerLanguage.${question.language}`]: 1 }
      },
      { new: true }
    );
    if(updatedInterest && updatedInterest.name && updatedInterest.numQuestions == 5 && !excludedUniverseLinkingKeyWords.has(updatedInterest.name.toLowerCase())){
      await Question.updateMany(
        { keywords: updatedInterest.name },
        { $addToSet: { linkedKeywords: updatedInterest.name } }
      );
      await Comment.updateMany(
        { keywords: updatedInterest.name },
        { $addToSet: { linkedKeywords: updatedInterest.name } }
      );
    }
  }
  return question;
}

async function addWebIdToQuestions() {
  let i = 0;
  const questions = await Question.find({ webId: { $exists: false } }, '_id');
  for await (const question of questions) {
    i += 1;
    question.webId = await webIdLib.getWebId();
    console.log(i, question.webId);
    await question.save();
  }
  return questions.length;
}

async function createSitemap() {
  try {
    console.log(`createSitemap - createSitemap function: question sitemap creation initiated`);
    const pipeline = [
      {
        $match: {
          createdAt: { $lte: new Date() },
          banned: false,
          numComments: { $gt: 4 },
        },
      },
      {
        $lookup: {
          from: 'users',
          localField: 'createdBy',
          foreignField: '_id',
          as: 'createdBy',
        },
      },
      {
        $unwind: {
          path: '$createdBy',
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $match: {
          'createdBy.shadowBanned': { $ne: true }
        },
      },
      {
        $project: {
          parent: 1,
          interestName: 1,
          webId: 1,
          title: 1,
          text: 1,
          language: 1,
          createdAt: 1,
          lastCommentAddedTime: 1,
        },
      },
      {
        $sort: { createdAt: -1 },
      },
    ];
    const cursor = Question.aggregate(pipeline).cursor({ batchSize: 1000 })
    const urls = [];
    let processedCount = 0;
    for await (const question of cursor) {
      processedCount++;
      if (processedCount % 1000 == 0) {
        console.log(`createSitemap - Processing question ${processedCount}`);
      }
      urls.push({
        url: generateQuestionUrl(question, true),
        lastModified: question.lastCommentAddedTime || question.createdAt,
      });
    }
    console.log(`createSitemap - Total questions processed: ${processedCount}`);
    return urls;
  } catch (err) {
    console.log(`createSitemap - createSitemap function error handler: error for question and error is ==`, err);
  }
}

async function createInterestSitemap() {
  const query = {
    status: null,
    numQuestions: { $gte: 5 },
  };
  const projection = 'name lastPostAddedTime createdAt';
  const interests = await Interest
    .find(query, projection)
    .lean();
  const urls = interests.map((x) => generateInterestObjectForSitemap(x));
  return urls;
}

async function createQod({
  createdBy,
  text,
  language,
}) {
  const lastQod = await Question.findOne({ interestName: 'questions', language }).sort({ createdAt: -1 });
  let newDate;

  if (!lastQod) {
    newDate = new Date();
  } else {
    newDate = DateTime.fromJSDate(lastQod.createdAt).plus({ days: 1 }).toJSDate();
    if (newDate < new Date()) {
      newDate = new Date();
    }
  }
   const question = await createQuestion({
    interestName: 'questions',
    hashtags: ['questions'],
    text,
    createdBy,
    createdAt: newDate,
    language,
  });
  await Question.updateSearchFields(question._id)
  return question;
}

async function addHiveFlag(post, text) {
  if(!text) return
  const res = await isTextInappropriate(text);
  if (res) {
    post.banned = true;
    post.bannedDate = Date.now();
    post.flaggedByHive = true;
    post.hiveClass = res.class;
    post.hiveScore = res.score;
  }
}

function getUserAttributes(user) {
  return {
    age: user.age,
    minAge: user.preferences.minAge,
    maxAge: user.preferences.maxAge,
    genderPreferenceHash: user.genderPreferenceHash,
    status: user.verification.status,
    mbti: user.personality.mbti,
    countryCode: user.countryCode,
    city: user.city,
    state: user.state,
    enneagram: user.enneagram,
    horoscope: user.horoscope,
    latitude2: user.latitude2,
    longitude2: user.longitude2,
  };
}

async function backFillUserAttributes() {
  const users = mongoose.connection.db.collection('users');
  const query = { 'metrics.numQuestions': { $gt: 0 } };
  const options = {};
  const cursor = users.find(query, options);
  let i = 0;
  let bulk = Question.collection.initializeUnorderedBulkOp();
  for await (const user of cursor) {
    bulk.find({ createdBy: user._id }).update({ $set: { userAttributes: getUserAttributes(user) } });
    i++;
    if (i % 100 == 0) {
      const res = await bulk.execute();
      console.log(i, res);
      bulk = Question.collection.initializeUnorderedBulkOp();
    }
  }
  const res = await bulk.execute();
  console.log(i, res);
}

async function backfillLinkedKeywords() {
  const approvedInterests = await Interest.distinct('name', {status: null,  numQuestions: { $gte: 5 }});

  {
    const collection = mongoose.connection.db.collection('questions');
    const query = {};
    const options = {
      projection: {
        _id: 1,
        keywords: 1,
      },
    };
    const cursor = collection.find(query, options);
    let i = 0;
    let bulk = Question.collection.initializeUnorderedBulkOp();
    for await (const doc of cursor) {
      const filter = { _id: doc._id };
      const linkedKeywords = doc.keywords.filter(keyword => approvedInterests.includes(keyword));
      const updates = {
        $set: {
          linkedKeywords,
        },
      };
      bulk.find(filter).update(updates);
      i++;
      if (i % 100 == 0) {
        const res = await bulk.execute();
        console.log(i, res);
        bulk = Question.collection.initializeUnorderedBulkOp();
      }
    }
    const res = await bulk.execute();
    console.log(i, res);
  }

  {
    const collection = mongoose.connection.db.collection('comments');
    const query = {};
    const options = {
      projection: {
        _id: 1,
        text: 1,
      },
    };
    const cursor = collection.find(query, options);
    let i = 0;
    let bulk = Comment.collection.initializeUnorderedBulkOp();
    for await (const doc of cursor) {
      const filter = { _id: doc._id };
      const keywords = Array.from(new Set([
        ...extractValidWords(doc.text || ''),
      ]));
      const linkedKeywords = keywords.filter(keyword => approvedInterests.includes(keyword));
      const updates = {
        $set: {
          keywords,
          linkedKeywords,
        },
      };
      bulk.find(filter).update(updates);
      i++;
      if (i % 1000 == 0) {
        const res = await bulk.execute();
        console.log(i, res);
        bulk = Comment.collection.initializeUnorderedBulkOp();
      }
    }
    const res = await bulk.execute();
    console.log(i, res);
  }
}

async function backfillCommentInterestName() {
  await Comment.aggregate([
    {
      $match: {
        interestName: null,
      }
    },
    {
      $lookup: {
        from: 'questions',
        localField: 'question',
        foreignField: '_id',
        as: 'questions',
      }
    },
    {
      $project: {
        interestName: { $first: '$questions.interestName' },
        language: { $first: '$questions.language' },
      }
    },
    {
      $merge: {
        into: 'comments',
        whenMatched: 'merge',
        whenNotMatched: 'discard',
      }
    },
  ]);
}

async function backfillInterestPoints() {

  // calculate points
  console.log('backfillInterestPoints: start calculating points');
  await Question.aggregate([
    {
      $unionWith: {
        coll: 'comments',
      }
    },
    {
      $match: {
        createdBy: { $ne: null },
        interestName: { $ne: null },
        language: { $ne: null },
        $or: [
          { numLikes: { $gt: 0 } },
          { numUsersThatCommented: { $gt: 0 } },
        ],
      }
    },
    {
      $group: {
        _id: {
          createdBy: '$createdBy',
          interestName: '$interestName',
          language: '$language',
        },
        points: {
          $sum: {
            $add: [
              { $ifNull: [ '$numLikes', 0 ] },
              { $ifNull: [ '$numUsersThatCommented', 0 ] },
            ]
          }
        }
      }
    },
    {
      $lookup: {
        from: 'users',
        localField: '_id.createdBy',
        foreignField: '_id',
        as: 'userObj',
      },
    },
    {
      $unwind: '$userObj',
    },
    {
      $project: {
        _id: 0,
        user: "$_id.createdBy",
        interest: "$_id.interestName",
        language: "$_id.language",
        points: "$points",
        shadowBanned: { $cond: [ '$userObj.shadowBanned', true, '$$REMOVE' ] },
      },
    },
    {
      $out: 'interestpoints'
    },
  ]);
  console.log('backfillInterestPoints: done calculating points');

  {
    // calculate ranks
    console.log('backfillInterestPoints: start calculating ranks');
    const interests = await Interest.find({ numQuestions: { $gt: 0 } }, 'name numQuestionsPerLanguage').lean();
    console.log(`Found ${interests.length} interests`);

    let promisesArr = [];
    for (const interest of interests) {
      for (const language in interest.numQuestionsPerLanguage) {
        promisesArr.push(updateInterestRanks(interest.name, language));
        if (promisesArr.length > 100) {
          await Promise.all(promisesArr);
          promisesArr = [];
        }
      }
    }
    await Promise.all(promisesArr);
    console.log('backfillInterestPoints: done calculating points');
  }

  // copy to users
  console.log('backfillInterestPoints: start copying to users');
  await InterestPoint.aggregate([
    {
      $project: {
        _id: 0,
        user: "$user",
        interestPoints: {
          interest: "$interest",
          language: "$language",
          points: "$points",
          rank: "$rank",
        },
      },
    },
    {
      $group: {
        _id: "$user",
        interestPoints: {
          $push: "$interestPoints",
        },
      },
    },
    {
      $merge: {
        into: 'users',
        whenMatched: 'merge',
        whenNotMatched: 'discard',
      }
    },
  ]);
  console.log('backfillInterestPoints: done copying to users');
}

async function calculateInterestPointsForInterest(interest) {
  const interestName = interest.name;
  await InterestPoint.deleteMany({ interest: interestName });
  console.log(`Deleted old interestPoints for interest: ${interestName}`);

  console.log(`backfillInterestPoints for interest: ${interestName}, start calculating points`);
  await Question.aggregate([
    {
      $unionWith: { coll: 'comments' },
    },
    {
      $match: {
        createdBy: { $ne: null },
        interestName: interestName,
        language: { $ne: null },
        $or: [
          { numLikes: { $gt: 0 } },
          { numUsersThatCommented: { $gt: 0 } },
        ],
      },
    },
    {
      $group: {
        _id: {
          user: '$createdBy',
          interest: '$interestName',
          language: '$language',
        },
        points: {
          $sum: {
            $add: [
              { $ifNull: ['$numLikes', 0] },
              { $ifNull: ['$numUsersThatCommented', 0] },
            ],
          },
        },
      },
    },
    {
      $lookup: {
        from: 'users',
        localField: '_id.user',
        foreignField: '_id',
        as: 'userObj',
      },
    },
    { $unwind: '$userObj' },
    {
      $project: {
        user: '$_id.user',
        interest: '$_id.interest',
        language: '$_id.language',
        points: '$points',
        shadowBanned: { $cond: ['$userObj.shadowBanned', true, false] },
      },
    },
    {
      $merge: {
        into: 'interestpoints',
        whenNotMatched: 'insert',
      },
    },
  ]);

  console.log(`backfillInterestPoints for interest: ${interestName}, done calculating points`);

  // Calculate ranks
  console.log(`backfillInterestPoints: start calculating ranks for interest '${interestName}'`);
  const languages = Object.keys(interest.numQuestionsPerLanguage || {});
  let promisesArr = [];

  for (const language of languages) {
    promisesArr.push(updateInterestRanks(interestName, language));
    if (promisesArr.length >= 100) {
      await Promise.all(promisesArr);
      promisesArr = [];
    }
  }
  await Promise.all(promisesArr);
  console.log(`backfillInterestPoints: done calculating ranks for interest '${interestName}'`);

  // Copy to users
  console.log('backfillInterestPoints: start copying to users');
  const interestPoints = await InterestPoint.find({ interest: interestName }).lean();

  const bulk = User.collection.initializeUnorderedBulkOp();
  for (const ip of interestPoints) {
    // If user already has that interest + language → update
    bulk.find({
      _id: ip.user,
      'interestPoints.interest': ip.interest,
      'interestPoints.language': ip.language,
    }).updateOne({
      $set: {
        'interestPoints.$.points': ip.points,
        'interestPoints.$.rank': ip.rank,
      },
    });

    // If not exists → push new
    bulk.find({
      _id: ip.user,
      $nor: [
        {
          interestPoints: {
            $elemMatch: {
              interest: ip.interest,
              language: ip.language,
            },
          },
        },
      ],
    }).updateOne({
      $push: {
        interestPoints: {
          interest: ip.interest,
          language: ip.language,
          points: ip.points,
          rank: ip.rank,
        },
      },
    });
  }

  if (bulk.length > 0) {
    const res = await bulk.execute();
    console.log(
      `backfillInterestPoints: done copying points to users for interest '${interestName}', matched ${res.nMatched || res.nMatchedCount || 0}, modified ${res.nModified || res.nModifiedCount || 0}`
    );
  } else {
    console.log(`backfillInterestPoints: nothing to update for interest '${interestName}'`);
  }
}

async function validateMentionedUsers(mentionedUsers) {
  if (mentionedUsers.length > 10) {
    mentionedUsers = mentionedUsers.slice(0, 10);
  }
  return await User.find({_id: {$in: mentionedUsers.map(x => x._id)}}, '_id firstName fcmToken locale appVersion');
}

async function notifyMentionedUsers(mentionedUsers, post, postType, data, mentionedBy, mentionedText, parentPost = null) {
  if (!mentionedUsers || !mentionedUsers.length) {
    return;
  }
  const needToNotify = mentionedUsers.filter(x => !post.mentionedUsersNotified.includes(x._id));
  for (mentionedUser of needToNotify) {
    if(!mentionedUser.versionAtLeast('1.13.74') && (post.postedAnonymously || parentPost?.postedAnonymously)) break
    admin.sendNotification(
      mentionedUser,
      null,
      translate(
        '%s mentioned you in a ' + (postType == 'question' ? 'post' : 'comment'),
        mentionedUser.locale,
        post.postedAnonymously? mentionedBy.anonymousProfileNickname : mentionedBy.firstName,
      ),
      mentionedText,
      data,
      null,
      'social',
      'mentioned-in-post',
    );
    await updateNotification(
      mentionedUser._id,
      post,
      postType,
      'mention',
      JSON.stringify(data),
      post.postedAnonymously? null : mentionedBy,
      1,
    );
    post.mentionedUsersNotified.push(mentionedUser._id);
  }
  await post.save();
}

async function backfillQuestionHashtags() {
  const res = await Question.updateMany(
    { interestName: { $exists: true } },
    [ { $set: { hashtags: [ '$interestName' ] } } ],
  );
  console.log(res);
}

async function backfillIsVideo() {
  const res = await Question.updateMany(
    { convertedVideo: { $ne: null } },
    { isVideo: true },
  );
  console.log(res);
}

async function backfillLinkedPillarKeywords() {

  {
    const collection = mongoose.connection.db.collection('questions');
    const query = {};
    const options = {
      projection: {
        _id: 1,
        tile: 1,
        text: 1,
      },
    };
    const cursor = collection.find(query, options);
    let i = 0;
    let bulk = Question.collection.initializeUnorderedBulkOp();
    for await (const doc of cursor) {
      const filter = { _id: doc._id };
      const linkedPillarKeywords = findLinkedPillarKeywords(((doc.title || '') + ' ' + (doc.text || '')).trim());
      const updates = {
        $set: {
          linkedPillarKeywords,
        },
      };
      bulk.find(filter).update(updates);
      i++;
      if (i % 100 == 0) {
        const res = await bulk.execute();
        console.log(i, res);
        bulk = Question.collection.initializeUnorderedBulkOp();
      }
    }
    const res = await bulk.execute();
    console.log(i, res);
  }

  {
    const collection = mongoose.connection.db.collection('comments');
    const query = {};
    const options = {
      projection: {
        _id: 1,
        text: 1,
      },
    };
    const cursor = collection.find(query, options);
    let i = 0;
    let bulk = Comment.collection.initializeUnorderedBulkOp();
    for await (const doc of cursor) {
      const filter = { _id: doc._id };
      const linkedPillarKeywords = findLinkedPillarKeywords(doc.text);
      const updates = {
        $set: {
          linkedPillarKeywords,
        },
      };
      bulk.find(filter).update(updates);
      i++;
      if (i % 1000 == 0) {
        const res = await bulk.execute();
        console.log(i, res);
        bulk = Comment.collection.initializeUnorderedBulkOp();
      }
    }
    const res = await bulk.execute();
    console.log(i, res);
  }
}

async function backfillLinkedExploreKeywords() {

  {
    const collection = mongoose.connection.db.collection('questions');
    const query = {};
    const options = {
      projection: {
        _id: 1,
        tile: 1,
        text: 1,
      },
    };
    const cursor = collection.find(query, options);
    let i = 0;
    let bulk = Question.collection.initializeUnorderedBulkOp();
    for await (const doc of cursor) {
      const filter = { _id: doc._id };
      const linkedExploreKeywords = Array.from(new Set([
        ...findLinkedExploreKeywords(doc.title || ''),
        ...findLinkedExploreKeywords(doc.text || ''),
      ]));
      const updates = {
        $set: {
          linkedExploreKeywords,
        },
      };
      bulk.find(filter).update(updates);
      i++;
      if (i % 100 == 0) {
        const res = await bulk.execute();
        console.log(i, res);
        bulk = Question.collection.initializeUnorderedBulkOp();
      }
    }
    const res = await bulk.execute();
    console.log(i, res);
  }

  {
    const collection = mongoose.connection.db.collection('comments');
    const query = {};
    const options = {
      projection: {
        _id: 1,
        text: 1,
      },
    };
    const cursor = collection.find(query, options);
    let i = 0;
    let bulk = Comment.collection.initializeUnorderedBulkOp();
    for await (const doc of cursor) {
      const filter = { _id: doc._id };
      const linkedExploreKeywords = Array.from(new Set([
        ...findLinkedExploreKeywords(doc.text || ''),
      ]));
      const updates = {
        $set: {
          linkedExploreKeywords,
        },
      };
      bulk.find(filter).update(updates);
      i++;
      if (i % 1000 == 0) {
        const res = await bulk.execute();
        console.log(i, res);
        bulk = Comment.collection.initializeUnorderedBulkOp();
      }
    }
    const res = await bulk.execute();
    console.log(i, res);
  }
}

const maleTestimonials = [
  '64cc42eb16ac601840da523a',
  '64cf2a9adfca4595e7cea8ec',
  '644aa35bb697667f2d3b910c',
  '642d1b86dfaf67a3df7d4d3e',
  '63dd981efae677c83cf61962',
  '625885b7244a0524956e0402',
].map(x => formatId(x));

const femaleTestimonials = [
  '648bc938fa81646c02849a70',
  '64de4e6e8d41619a1e336295',
  '64d0a89c2efff1b7af7f9c70',
  '64661749fc869f1cfb8b7242',
  '641aac45b141e53d3ef95299',
  '63d4ec43f75e7aecafc1e442',
  '64b2390b1f4fbaf48ea81a4e',
  '64c5df70abc105c687c7373e',
  '64fdbc0021ba4de3927f58b5',
].map(x => formatId(x));

const allTestimonials = maleTestimonials.concat(femaleTestimonials);

async function getTestimonials(user) {
  const gender = user.gender;
  let postIds;
  if (gender == 'male') {
    postIds = maleTestimonials;
  } else if (gender == 'female') {
    postIds = femaleTestimonials;
  } else {
    postIds = _.sample(allTestimonials, 6);
  }

  const matchBy = {
    _id: { $in: postIds },
  };

  const questions = await getQuestions(
    user,
    null,
    null,
    matchBy,
  );
  return questions;
}

async function moderatePostUsingOpenai(text, question, comment) {
  const lower = text.toLowerCase();
  let locale;
  let keyword;
  if (comment) {
    locale = comment?.detectedLanguage || comment?.language || 'en';
  } else {
    locale = question?.detectedLanguage || question?.language || 'en';
  }

  if (['zh', 'zh-Hans', 'zh-Hant', 'ja', 'th', 'my'].includes(locale)) {
    keyword = postModerationKeywords.find((s) => lower.includes(s));
  } else {
    keyword = postModerationKeywords.find((s) => new RegExp(`(^|[^\\p{L}\\p{N}])${s}(?=[^\\p{L}\\p{N}]|$)`, 'gu').test(lower));
  }

  if (!keyword) {
    return;
  }
  let comments = [];
  if (comment) {
    let parentComment;
    if (!comment.parent.equals(question._id)) {
      parentComment = await Comment.findOne({ _id: comment.parent }, '_id text').lean();
      parentComment.replies = await Comment.find({ parent: parentComment._id }, '_id text').sort('createdAt').lean();
    } else {
      parentComment = {
        _id: comment._id,
        text: comment.text,
        replies: [],
      };
    }
    comments.push(parentComment);
  }
  const openaiResult = await openai.moderatePosts(question, comments);
  if (openaiResult.output) {
    try {
      const output = openaiResult.output;
      console.log(output);
      // remove markdown formatting before parsing
      parsed = JSON.parse(output.replace('```json','').replace('```',''));
      console.log(parsed);

      for (const entry of parsed) {
        if (entry.postId && entry.postId == question._id && entry.ban !== undefined) {
          let update = {};
          update.flaggedByOpenai = entry.ban;
          if (entry.ban) {
            update.banned = true;
            update.bannedBy = 'openai';
            update.bannedDate = new Date();
            update.bannedReason = entry.explanation;
          }
          await Question.updateOne({ _id: question._id }, update);
        }
        else if (entry.commentId && entry.ban !== undefined) {
          if (comments.some(x => x._id == entry.commentId || x.replies.some(y => y._id == entry.commentId))) {
            let update = {};
            update.flaggedByOpenai = entry.ban;
            if (entry.ban) {
              update.banned = true;
              update.bannedBy = 'openai';
              update.bannedDate = new Date();
              update.bannedReason = entry.explanation;
            }
            await Comment.updateOne({ _id: entry.commentId }, update);
          }
        }
      }
    } catch (err) {
      console.log(err);
      openaiResult.isError = true;
      openaiResult.errorMessage = `json parsing error: ${err.message}`;
    }
  }
  await PostModeration.create({
    question: question._id,
    url: generateQuestionUrl(question),
    keyword: keyword,
    openai: openaiResult,
  });
}

module.exports = {
  getPosts,
  getQuestions,
  getLikes,
  updateQuestionScore,
  updateQuestionScores,
  updateAllQuestionScores,
  updateCommentScore,
  updateCommentScores,
  likePost,
  unlikePost,
  reportPost,
  banPost,
  dismissPost,
  parseSortAndPagingParams,
  incrementNumComments,
  updateNotification,
  markNotificationSeen,
  formatQuestion,
  formatComment,
  getQuestion,
  getQuestionInternalLinking,
  getQuestionsBasedOnProfilesName,
  getQuestionsBasedOnProfilesSubCategory,
  getQuestionFeedRouteHandler,
  getQuestionFeed,
  getQuestionAllQuestions,
  getComment,
  getSingleCommentById,
  getCommentContext,
  createQuestion,
  addWebIdToQuestions,
  backfillAllUserKarma: backfillAllUserKarmaV2,
  backfillInterestNumQuestions,
  backfillInterestNumQuestionsPerLanguage,
  getUserQuestions,
  getUserComments,
  createSitemap,
  createInterestSitemap,
  awardPost,
  getAwardSenders,
  createQod,
  createSlug,
  addHiveFlag,
  incrementKarma,
  backFillUserAttributes,
  getUserAttributes,
  backfillLinkedKeywords,
  validateMentionedUsers,
  notifyMentionedUsers,
  backfillQuestionHashtags,
  backfillLinkedPillarKeywords,
  backfillLinkedExploreKeywords,
  generateQuestionUrl,
  incrementUserKarma,
  backfillIsVideo,
  getTestimonials,
  moderatePostUsingOpenai,
  backfillCommentInterestName,
  backfillInterestPoints,
  updateInterestRanks,
  getUserAnonymousQuestions,
  getUserAnonymousComments,
  addNumQuestionsToInterest,
  calculateInterestPointsForInterest,
  isPostVisible,
  useProfilePreview,
  formatProfilePreview,
  getWeightedScore,
  createdByDefaultFilter,
};
