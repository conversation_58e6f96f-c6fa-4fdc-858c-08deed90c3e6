const { DateTime } = require('luxon');
const constants = require('../lib/constants');

function hasDuplicates(array) {
  return (new Set(array)).size !== array.length;
}

function removeDuplicates(array) {
  if (!Array.isArray(array)) {
    return array;
  }
  return [...new Set(array)];
}

function getCurrentDayResetTime(timezone) {
  // reset at midnight local time

  const returnedResetHour = 0;
  const returnedResetMinute = 0;
  if (!timezone || !DateTime.local().setZone(timezone).isValid) {
    timezone = 'UTC';
  }
  const currentTime = DateTime.local().setZone(timezone);
  const returnedResetTime = currentTime.set({
    hour: returnedResetHour, minute: returnedResetMinute, second: 0, millisecond: 0,
  });
  const nextResetTime = currentTime < returnedResetTime
    ? returnedResetTime
    : returnedResetTime.plus({ days: 1 });
  return nextResetTime.toJSDate();
}

function getPreviousRewardResetTime(user, referenceDate) {
  let { timezone } = user;
  const rewardResetHour = 0;
  const rewardResetMinute = 0;
  if (!timezone || !DateTime.local().setZone(timezone).isValid) {
    timezone = 'UTC';
  }
  let referenceDatetime = referenceDate ? DateTime.fromJSDate(referenceDate) : DateTime.local();
  referenceDatetime = referenceDatetime.setZone(timezone);
  const rewardResetTime = referenceDatetime.set({
    hour: rewardResetHour, minute: rewardResetMinute, second: 0, millisecond: 0,
  });
  const previousResetTime = referenceDatetime < rewardResetTime
    ? rewardResetTime.minus({ days: 1 })
    : rewardResetTime;
  return previousResetTime.toJSDate();
}

function assignConfig(percentage) {
  return Math.random() < percentage;
}

function assignIntConfigDeprecated(numOptions) {
  let val = Math.floor(Math.random() * numOptions);
  if (val == numOptions - 1) {
    val = null;
  }
  return val;
}

function assignIntConfig(numOptions) {
  const val = Math.floor(Math.random() * numOptions);
  return val;
}

function arrayDiff(arr1 = [], arr2 = []) {
  if (!Array.isArray(arr1)) {
    arr1 = [];
  }
  if (!Array.isArray(arr2)) {
    arr2 = [];
  }
  return arr1.filter((x) => !arr2.includes(x));
}

function calculateViewableInDailyProfiles(user) {

  // do not show unverified web users with unfamiliar email domains until they verify
  if (constants.requireManualVerificationForWeb() && !user.isVerified() && user.signupSource == 'web' && user.emailDomain && !constants.knownEmailDomains.includes(user.emailDomain)) {
    return false;
  }

  const rv = !user.hidden
    && !user.banned
    && !user.shadowBanned
    && user.pictures.length > 0
    && user.location !== undefined
    && user.location !== null;

  return rv;
}

function isValidPicture(picture) {
  const lower = picture.toLowerCase();
  if (['.png','.jpeg','.jpg','.gif','.webp'].some(ext => lower.includes(ext))) {
    return true;
  }
  return false;
}

function isAppUser(user){
  return ['ios', 'android'].includes(user.os)
}

function extractEmailDomain(email) {
  if (typeof email !== 'string') {
    return null;
  }
  return email.toLowerCase().split('@')[1];
}

function checkIfCommonElementsInArray(array1, array2) {
  const a1 = Array.isArray(array1) ? array1 : [];
  const a2 = Array.isArray(array2) ? array2 : [];
  return a1.some(name => a2.includes(name));
}

function subtractArrays(array1, array2) {
  const a1 = Array.isArray(array1) ? array1 : [];
  const a2 = Array.isArray(array2) ? array2 : [];
  const set2 = new Set(a2);
  return a1.filter(item => !set2.has(item));
}

/**
 * Determines whether to run a feature for a user based on their creation timestamp
 * @param {Object} user - User object containing createdAt field
 * @param {number} percentage - The percentage chance to run (0-100)
 * @returns {boolean} True if the feature should run for this user
 */
function shouldRunForPercentageOfUser(user, percentage = 10) {
    if(process.env.NODE_ENV  === 'test-atlas'){
      return true;
    }
    if(process.env.NODE_ENV  === 'test'){
      return false; // return false to prevent other test cases fail
    }

    const testUsers_beta = ['vOvjZrleUFdcgxt4CvXMbPuYIen2','RikrpIKQ2yfYMGMMGsIpbZGGy8h2']
    if (process.env.NODE_ENV === 'beta' && user && testUsers_beta.includes(user._id)) {
      return true
    }

    const testUsers_prod = ['AqBAN0tsfAgZTZmvVxxGrbVW02r1','a3QrMqtPplanPfwDDUutnQ7Pa2s2']
    if (process.env.NODE_ENV === 'prod' && user && testUsers_prod.includes(user._id)) {
      return true
    }

    // Validate inputs
    if (!user || !user.createdAt) return false;
    if (percentage <= 0) return false;
    if (percentage >= 100) return true;
    
    // Parse the createdAt date (handles both Date objects and ISO strings)
    const createdAt = user.createdAt instanceof Date ? 
        user.createdAt : 
        new Date(user.createdAt);
    
    // Get the timestamp in milliseconds
    const timestamp = createdAt.getTime();
    
    // Calculate the modulo value needed for the desired percentage
    const modValue = Math.floor(100 / percentage);
    
    // Return true for users that fall into our target segment
    return timestamp % modValue === 0;
}


module.exports = {
  hasDuplicates,
  removeDuplicates,
  getCurrentDayResetTime,
  getPreviousRewardResetTime,
  assignConfig,
  assignIntConfig,
  assignIntConfigDeprecated,
  arrayDiff,
  calculateViewableInDailyProfiles,
  isValidPicture,
  isAppUser,
  extractEmailDomain,
  checkIfCommonElementsInArray,
  subtractArrays,
  shouldRunForPercentageOfUser
};
