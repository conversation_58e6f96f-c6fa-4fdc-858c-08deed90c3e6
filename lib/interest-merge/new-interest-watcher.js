const Interest = require('../../models/interest');
const { updateTranslatedInterests } = require('../interest');

let lastProcessedId = null;
let changeStream = null;

/**
 * Fetch and process missed documents
 */
const processMissedDocuments = async () => {
  if (!lastProcessedId) return;

  try {
    const missedDocs = await Interest.find({
      _id: { $gt: lastProcessedId },
      status: null,
      translations: { $exists: true },
    })
      .select('_id category name translations')
      .lean();

    if (missedDocs.length) {
      console.log(`Interest watcher: Processing ${missedDocs.length} missed documents.`);
      missedDocs.forEach(updateTranslatedInterests);
    }
  } catch (error) {
    console.log('Interest watcher: Error processing missed documents:', error);
  }
};

/**
 * Handles new document processing
 */
const handleNewDocument = (newDocument) => {
  console.log('Interest watcher: New document detected:', newDocument._id);
  try {
    if (newDocument.translations) {
      updateTranslatedInterests(newDocument);
    }
    lastProcessedId = newDocument._id;
  } catch (error) {
    console.log('Interest watcher: Error handling new document:', error);
  }
};

/**
 * Starts watching for changes in the Interest collection
 */
const startWatching = async () => {
  if (changeStream || process.env.NODE_ENV === 'test') return;
  const collection = Interest.collection;
  await processMissedDocuments();

  try {
    changeStream = collection.watch([{ $match: { operationType: 'insert' } }], { fullDocument: 'updateLookup' });

    changeStream.on('change', (change) => {
      if (!change.fullDocument) return;
      handleNewDocument(change.fullDocument);
    });

    changeStream.on('error', async (error) => {
      console.log('Interest watcher: Error on change stream watch:', error);
      await restartChangeStream();
    });

    console.log('Interest watcher: Change stream started');
    return changeStream;
  } catch (error) {
    console.log('Interest watcher: Failed to start change stream:', error);
  }
};

/**
 * Stops the change stream gracefully
 */
const stopWatching = async () => {
  try {
    if (changeStream) {
      changeStream.removeAllListeners();
      await changeStream.close();
      console.log('Interest watcher: Change stream stopped.');
    }
  } catch (error) {
    console.log('Interest watcher: Failed to stop change stream:', error);
  }
};

/**
 * Restarts the change stream if an error occurs
 */
const restartChangeStream = async () => {
  try {
    await stopWatching();
    await new Promise((resolve) => setTimeout(resolve, 1000));
    await startWatching();
    console.log('Interest watcher: Change stream restarted, processing from:', lastProcessedId);
  } catch (error) {
    console.log('Interest watcher: Failed to restart change stream:', error);
  }
};

module.exports = { startWatching };
