const InterestCountryCount = require('../../models/interest-country-count');
const openaiClient = require('../openai-client');
const { locales } = require('../translate');

const client = openaiClient.getOpenaiClient();

const languages = locales.filter((locale) => locale !== null).join(', ');

const prepareTranslationPrompt = (
  name,
  similarInterests,
) => `Purpose: Localize the following hashtag with fluency and naturalness in the target locale, targeting a young, informal audience. Check for appropriate use of contemporary, casual language, and ensure it maintains the tone and intent of the original text. You must NOT change the meaning from the original language, even if you believe it to be incorrect.

Instruction:
- Translate the hashtag into the following locales:
  ${languages}
- If a matching or relevant translation exists in the provided list of similar interests (${similarInterests}), for any locale, use that translation as the output.
- If no relevant match is found, translate the hashtag into the requested locale concisely and appropriately.
- Output only the localized material in strict JSON format as shown below.
- Do not add introductions, explanations, comments, or any extra text.
- Do not translate 'Bo<PERSON>' (the name of the app).

Original Hashtag: ${name}

Output format (strict JSON):
{
  "af": "translation",
  "sq": "translation",
  "bn": "translation",
  "ar": "translation",
  ...
}`;

const prepareSimilarityPrompt = (
  mainInterest,
  similarInterests,
) => `Determine if the given interest (hashtag) is identical in meaning to, and should be merged with, any of the interests in the existing list.
    
Merging Rules:
Merge only if all of the following are true:
1. The given term is a direct translation or a commonly accepted abbreviation.
2. The meanings are identical and entirely interchangeable, so that merging them would not cause a loss of nuance.
3. Both terms refer to the same entity, character, or concept in the same context.

Do NOT merge if:
1. The terms refer to distinct entities, even if they share a similar or related context (e.g., different characters in the same story, or different installments in a franchise). In these cases, the terms are not interchangeable because they represent different things.
2. The terms share only a word fragment, common stem, or phonetic similarity without being fully synonymous.
3. One term is a broad category (e.g., music, design, movies) while the other is a specific niche or subcategory (e.g., pop music, graphic design, action movies).
4. The terms belong to distinct communities, subcultures, or practices, even if they seem related (e.g., self-care vs. mental health), or the terms have a specialized or unique meaning in particular contexts which would be lost if merged. 

Important: Always consider the specific meaning of each term in the context in which it is used. If the terms refer to different characters, concepts, or entities, they should not be merged.

Proposed interest: "${mainInterest}"

Interests to Compare with:
${similarInterests.map((interest) => `"${interest}"`).join(', ')}

Output Format (JSON only):
- If the proposed interest is semantically identical and should be merged with an existing interest:
  { "sameas": "matched_hashtag" }
- If no match is found and the terms should not be merged:
  { "sameas": "" }

For multiple potential matches: Choose the one that best retains the intended meaning of the proposed interest, prioritizing cultural and contextual accuracy.`;

const withTimeout = (promise) => {
  // 60 seconds timeout
  const millis = 60000;
  const timeout = new Promise((resolve, reject) => setTimeout(() => reject(new Error('Timeout')), millis));
  return Promise.race([promise, timeout]);
};

const translateInterest = async (interestName, similarInterests, noTimeout) => {
  try {
    let output;
    const prompt = prepareTranslationPrompt(interestName, similarInterests.join(', '));
    const requestObject = {
      model: 'gpt-4o',
      response_format: {
        type: 'json_object',
      },
      messages: [
        {
          role: 'user',
          content: [
            {
              type: 'text',
              text: prompt,
            },
          ],
        },
      ],
    };
    if (noTimeout) {
      const response = await openaiClient.getOpenaiClient().chat.completions.create(requestObject);
      output = response.choices[0].message.content;
    } else {
      const response = await withTimeout(openaiClient.getOpenaiClient().chat.completions.create(requestObject));
      output = response.choices[0].message.content;
    }

    const parsed = JSON.parse(output.replace('```json', '').replace('```', ''));
    return parsed;
  } catch (error) {
    console.log('Error in interest translation from OPEN AI', error.message);
    return null;
  }
};

const validateInterestSimilarity = async (interestName, similarInterests, noTimeout) => {
  try {
    let output;
    const prompt = prepareSimilarityPrompt(interestName, similarInterests);
    const requestObject = {
      model: 'gpt-4o-mini',
      messages: [
        {
          role: 'user',
          content: [
            {
              type: 'text',
              text: prompt,
            },
          ],
        },
      ],
      response_format: {
        type: 'json_object',
      },
      temperature: 0.5,
      top_p: 1,
    };

    if (noTimeout) {
      const response = await openaiClient.getOpenaiClient().chat.completions.create(requestObject);
      output = response.choices[0].message.content;
    } else {
      const response = await withTimeout(openaiClient.getOpenaiClient().chat.completions.create(requestObject));
      output = response.choices[0].message.content;
    }

    const parsed = JSON.parse(output.replace('```json', '').replace('```', ''));
    return parsed;
  } catch (error) {
    console.log('Error in openai request for validate similarity', error);
    return null;
  }
};

// Helper function to merge an interest into the mainInterest
function mergeInterests(mainInterest, similarInterest) {
  // Merge category and libCategory if not already set
  mainInterest.category ||= similarInterest.category;
  mainInterest.libCategory ||= similarInterest.libCategory;

  // Merge the number of followers and questions
  mainInterest.numFollowers += similarInterest.numFollowers;
  mainInterest.numQuestions += similarInterest.numQuestions;
}

const generateVector = async (name) => {
  try {
    const requestObject = {
      model: 'text-embedding-3-small',
      input: name,
      dimensions: 256,
    };

    const response = await client.embeddings.create(requestObject);
    return response.data[0].embedding;
  } catch (error) {
    console.log('Error in interest embedding from OPEN AI', error.message);
    return null;
  }
};

const validateMerge = async (interest, mergedWith) => {
  try {
    const prompt = `Classify the following word pairs as either 'Same' or 'Different.' 'Same' if the terms are interchangeable in common contexts or refer to the same concept or category, or one term is a mis-spelling. 'Different' if the terms refer to distinct entities, subclasses, or contexts, even if they are related. Please only output "Same" or "Different".
Term 1: ${interest} Term 2: ${mergedWith}`;

    const response = await openaiClient.getOpenaiClient().chat.completions.create({
      model: 'gpt-4o-mini',
      messages: [{ role: 'user', content: prompt }],
    });

    return response?.choices?.[0]?.message?.content?.trim() || 'Different';
  } catch (error) {
    console.log('Error in OpenAI request for validateMerge:', error);
    return null;
  }
};

const mergeInterestCountryCounts = async (mainInterest, mergedInterests) => {
  try {
    const primaryDocs = await InterestCountryCount.find({
      interestName: mainInterest,
    });

    const primaryMap = new Map();
    for (const doc of primaryDocs) {
      const key = `${doc.country}__${doc.locale}`;
      primaryMap.set(key, doc);
    }

    const mergeDocs = await InterestCountryCount.find({
      interestName: { $in: mergedInterests },
    });

    const updates = [];
    const deletes = [];
    const renames = [];

    for (const doc of mergeDocs) {
      const key = `${doc.country}__${doc.locale}`;
      const primaryDoc = primaryMap.get(key);

      if (primaryDoc) {
        updates.push({
          updateOne: {
            filter: { _id: primaryDoc._id },
            update: { $inc: { count: doc.count } },
          },
        });

        deletes.push({
          deleteOne: { filter: { _id: doc._id } },
        });
      } else {
        renames.push({
          updateOne: {
            filter: { _id: doc._id },
            update: { $set: { interestName: mainInterest } },
          },
        });

        primaryMap.set(key, doc);
      }
    }

    const operations = [...updates, ...deletes, ...renames];
    if (operations.length) {
      await InterestCountryCount.bulkWrite(operations);
    }
  } catch (err) {
    console.log('Error during mergeInterestCountryCounts:', err);
  }
};

module.exports = { generateVector, validateMerge, translateInterest, mergeInterests, validateInterestSimilarity, mergeInterestCountryCounts };
