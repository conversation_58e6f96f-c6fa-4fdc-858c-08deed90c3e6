[{"Interest": "music", "SimilarInterests": "música,musik,музыка,müzik,musique,musica,muzyka,âm<PERSON><PERSON><PERSON>,hudba,الموسيقى,muzica,музика,muziek,موسیقی,μουσική,ดนตรี,zene,音乐,מוסיקה,음악,音楽,muzika,音樂,musiikki,მუსიკა,glasba,musiqi,সঙ্গীত,muzik", "onBoarding": true}, {"Interest": "gaming", "SimilarInterests": "游戏,pela<PERSON>n,ゲーミング,גיימינג,гейминг,ch<PERSON><PERSON><PERSON>,เกม,게임,بازی", "onBoarding": true}, {"Interest": "movies", "SimilarInterests": "películas,filmes,film,фильмы,films,filmy,cine,الأفلام,фільми,filmek,ταινίες,kino,فیلمها,电影,סרטים,映画,電影,elokuvat,ფილმები,филми,moveis,кинофильмы,動画", "onBoarding": true}, {"Interest": "anime", "SimilarInterests": "аниме,أنمي,อนิเมะ,аніме,アニメ,動漫,动漫,애니,انیمه,אנימה,anim3", "onBoarding": true}, {"Interest": "food", "SimilarInterests": "comida,еда,makanan,essen,ye<PERSON><PERSON>,cibo,jed<PERSON><PERSON>,jí<PERSON>o,الطعام,eten,อาหาร,φαγητό,їжа,غذا,음식,食べ物,אוכל,jedlo,mat,ruoka,hrana,храна,maistas,jidlo,nourriture,comidaa", "onBoarding": true}, {"Interest": "travel", "SimilarInterests": "viajes,voyage,viagem,seyahat,reisen,podr<PERSON><PERSON>e,السفر,dul<PERSON>ch,cestování,旅行,uta<PERSON><PERSON>,reizen,ταξίδια,سفر,여행,טיולים,cestovanie,viagens,traveling,resor,пътуване,viajar,matkus<PERSON>n,მოგზაურობა,rejse,putovanje,kelion<PERSON><PERSON>,potovanje,səyahət,travelling,viatjar,viaggiare,viaje,podroże,gezmek,podroze,cestova<PERSON>,travels,trave,seyahatetmek,viaja,viajando,подорож,calatorie,การเดินทาง,journey,yolculuk", "onBoarding": true}, {"Interest": "romance", "SimilarInterests": "romantik,romantika,romantiek,浪漫,романтика,רומנטיקה,โรแมนซ์,로맨스,რომანტიკა,romantiikka,रोमांस,ロマンス,romansa", "onBoarding": true}, {"Interest": "memes", "SimilarInterests": "мемы,mèmes,меми,ממים,osmemes,الميم", "onBoarding": true}, {"Interest": "comedy", "SimilarInterests": "comedia,komedi,comédia,комедия,komedie,comédie,commedia,комедія,comedie,کمدی,komédie,קומדיה,komedija,komedia,კომედია,コメディ,comedi,comic<PERSON><PERSON>,víg<PERSON><PERSON><PERSON><PERSON>,κωμωδίες,comedies", "onBoarding": true}, {"Interest": "horror", "SimilarInterests": "horreur,horor,رعب,ホラー,ужас,ужасы,жахи", "onBoarding": true}, {"Interest": "cooking", "SimilarInterests": "cocinar,cozinhar,готовка,kochen,gotowanie,cucina,ye<PERSON><PERSON><PERSON><PERSON>,n<PERSON><PERSON><PERSON><PERSON>,pi<PERSON><PERSON><PERSON>,الط<PERSON><PERSON>,va<PERSON><PERSON><PERSON>,f<PERSON><PERSON><PERSON>,ทำอาหาร,آش<PERSON>زی,烹饪,בי<PERSON><PERSON><PERSON>,mat<PERSON><PERSON>,요리,料理,r<PERSON><PERSON><PERSON><PERSON>,烹飪,cocin<PERSON>,cook,masak,ye<PERSON><PERSON><PERSON><PERSON><PERSON>,кул<PERSON><PERSON><PERSON>,co<PERSON><PERSON><PERSON>,кулинарные", "onBoarding": true}, {"Interest": "outdoors", "SimilarInterests": "outdoor,extérieur,s<PERSON><PERSON><PERSON>,ng<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,户外,戶外,กลางแจ้ง,u<PERSON><PERSON><PERSON><PERSON>,airelibre,outside,buitenshuis,sorti,outandabout,занятиянаоткрытомвоздухе,야외활동,outdooractivities,passeiosaoarlivre,ulkoilu,アウトドア,outdooradventure,outdooradventures,passeioarlivre", "onBoarding": true}, {"Interest": "dogs", "SimilarInterests": "perros,cã<PERSON>,собаки,hunde,kö<PERSON>kler,chiens,psi,caini,kuty<PERSON>,honden,الكلاب,σκύλοι,سگها,כלבים,koirat,hundar,кучета,gossos,dogs️,chó,anjing,dog,perro,hund,chien,собака,หมา,강아지,cane,doggo,doggos,cachorros,cachoros", "onBoarding": true}, {"Interest": "funny", "SimilarInterests": "смешное,dr<PERSON><PERSON>,witzig,divertente,zabawne,مضحكة,ตลก,amuzant,grappig,смішне,vic<PERSON>,搞笑,αστεία,מצחי<PERSON>,sme<PERSON><PERSON>,სასაცილო,смешно,droles,ś<PERSON><PERSON><PERSON>,смешные,кумедне,roligt,sjovt", "onBoarding": true}, {"Interest": "rock", "SimilarInterests": "рок,ροκ,راک,rocks", "onBoarding": true}, {"Interest": "technology", "SimilarInterests": "tecnología,tecnologia,technologie,технологии,teknoloji,teknologi,technologia,التكنولوجيا,côngnghệ,tehnologie,технології,เทคโนโลยี,technológia,technologies,tecno,tech", "onBoarding": true}, {"Interest": "art", "SimilarInterests": "arte,искусство,sanat,kunst,sztuka,الفن,ศิลปะ,هنر,мистецство,műv<PERSON><PERSON><PERSON>,τέχνη,艺术,arta,אומנות,アート,umenie,藝術,الرسم,taide,ხელოვნება,미술,konst,изкуство,絵画,nghệthuật,uměn<PERSON>,umetnost", "onBoarding": true}, {"Interest": "animals", "SimilarInterests": "animales,animais,животные,animaux,hayvanlar,animali,tiere,z<PERSON><PERSON><PERSON><PERSON><PERSON>,zv<PERSON><PERSON><PERSON>,độ<PERSON><PERSON><PERSON>t,الحيوانات,állatok,dieren,тварини,สัตว์,حیوانات,動物,ζώα,z<PERSON><PERSON><PERSON>,동물,动物,חיות,el<PERSON><PERSON>t,djur,животни,ž<PERSON><PERSON><PERSON><PERSON>tang,animale,animal,hayvan,hewan,zv<PERSON><PERSON>e", "onBoarding": true}, {"Interest": "cats", "SimilarInterests": "gatos,кошки,kediler,katzen,gatti,kucing,mèo,القطط,แมว,kočky,коти,cat,pisici,katten,macsk<PERSON>,γάτες,고양이,گربهها,katter,mač<PERSON>,ma<PERSON><PERSON>,חתולים,katte,котки,gats,kedi,gatas,macska,neko,gatto,kočka,γατες", "onBoarding": true}, {"Interest": "books", "SimilarInterests": "libros,книги,liv<PERSON>,kitaplar,k<PERSON><PERSON><PERSON><PERSON>,bücher,libri,buku,الكتب,knihy,kö<PERSON><PERSON><PERSON>,boeken,βιβλία,کتابها,book,書籍,ספרי<PERSON>,böcker,knjige,kirjat,livro,kitablar,llibres,ksia<PERSON>ki,libro,ksia<PERSON><PERSON>,s<PERSON>ch,หนังสือ", "onBoarding": true}, {"Interest": "fantasy", "SimilarInterests": "fantasía,фэнтези,fante<PERSON>,fantasi,fantastyka,фентезі,fantezie,แฟนตาซี,فانتزی,פנטזיה,ファンタジー,fantaasia,fantasia,imagination,الخيال,φαντασία", "onBoarding": true}, {"Interest": "learning", "SimilarInterests": "apren<PERSON><PERSON><PERSON>,обучение,öğ<PERSON><PERSON>,apprentissage,apprendimento,التعلم,uczeni<PERSON>ę,学习,навчання,invatare,aprendizagem,učen<PERSON>,tanulás,למידה,學習,apren<PERSON><PERSON><PERSON>,aprender,belajar,lernen,เรียนรู้,học,learn", "onBoarding": true}, {"Interest": "photography", "SimilarInterests": "fotografía,fotografia,фотография,fotografie,fotografi,photographie,fotoğrafç<PERSON>lık,التصوير_الفوتوغرافي,fotografování,การถ่ายภาพ,nhi<PERSON><PERSON><PERSON><PERSON>,фотографія,f<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,φωτογραφία,عکاسی,摄影,사진,fotografering,写真,fotografovanie,攝影,צילום,fotografija,valokuvaus,fotograafia,ფოტოგრაფია,photografia,фотографии,fotózás,ফটোগ্রাফি,фотографировать,fotigrafia,photographing,fotografar,photographic,fotographie,fotografováni", "onBoarding": true}, {"Interest": "psychology", "SimilarInterests": "psicología,психология,psicologia,psychologie,psikoloji,psychologia,psikologi,علم_النفس,психологія,psihologie,pszichológia,ψυχολογία,روانشناسی,心理学,จิตวิทยา,psykologi,פסיכולוגיה,psychológia,心理學,psykologia,psihologija,psichologija,psycholog,piskoloji,tâmlýhọc", "onBoarding": true}, {"Interest": "history", "SimilarInterests": "historia,história,история,tarih,histoire,geschichte,التاريخ,historie,історія,تاریخ,ges<PERSON><PERSON><PERSON>,istorie,t<PERSON><PERSON><PERSON><PERSON><PERSON>,역사,sejarah,histori", "onBoarding": true}, {"Interest": "culture", "SimilarInterests": "cultura,культура,kültür,budaya,kultur,kultura,الثقافة,文化,kultúra,cultuur,فرهنگ,πολιτισμός,วัฒนธรรม,문화,култура", "onBoarding": true}, {"Interest": "rap", "SimilarInterests": "рэп,رپ,ραπ", "onBoarding": true}, {"Interest": "science", "SimilarInterests": "ciencia,ciência,наука,bilim,wissenschaft,scienze,nauka,sains,العلوم,věda,wetenschap,sti<PERSON>a,tudomány,科学,sciences,ciencias", "onBoarding": true}, {"Interest": "hiphop", "SimilarInterests": "хипхоп,χιπχοπ,ヒップホップ", "onBoarding": true}, {"Interest": "dating", "SimilarInterests": "namoro,kencan,rand<PERSON><PERSON><PERSON>,المواعدة,dejting,約會,约会,sez<PERSON><PERSON><PERSON>,randeni,sez<PERSON><PERSON><PERSON><PERSON>,t<PERSON><PERSON><PERSON><PERSON><PERSON>,свидание,побачення,นัดเดท,citas,rande,เดท,randi,ραντεβού,date,dates,cita,randka", "onBoarding": false}, {"Interest": "videos", "SimilarInterests": "ví<PERSON><PERSON>,videolar,vid<PERSON><PERSON>,فيديوهات,videa,ویدیوها,videók,vidios,vedios,filmiki,cortometraje", "onBoarding": true}, {"Interest": "single", "SimilarInterests": "soltero,solteiro,c<PERSON><PERSON><PERSON><PERSON>,j<PERSON><PERSON>,singiel,โสด,ne<PERSON><PERSON><PERSON>,all<PERSON><PERSON><PERSON>,singel,솔로,bekar,s<PERSON><PERSON>,單身,singlelife,single4life,singur,μονος,alone,sola,slobodn<PERSON>,libres", "onBoarding": false}, {"Interest": "metal", "SimilarInterests": "мета<PERSON><PERSON>,mé<PERSON>,мета<PERSON>,met<PERSON><PERSON>,μέταλ,メタル", "onBoarding": true}, {"Interest": "concerts", "SimilarInterests": "conciertos,концерты,koncerty,konzerte,kons<PERSON><PERSON>,concerti,konser,concertos,koncertek,concerten,συναυλίες,концерти,concerte,concert,کنسرتها,concerto", "onBoarding": true}, {"Interest": "action", "SimilarInterests": "acción,ação,aksiyon,azione,aksi,akcja,أك<PERSON><PERSON>,akční,actiune,екшн,действие", "onBoarding": true}, {"Interest": "singing", "SimilarInterests": "be<PERSON><PERSON>yi,пение,cantar,ś<PERSON><PERSON><PERSON>,singen,hát,ร้องเพลง,الغناء,zpěv,آوازخوانی,歌うこと,спів,menyanyi,sjunga,sing,canta,canto,노래", "onBoarding": true}, {"Interest": "drawing", "SimilarInterests": "dibujo,рисование,desen<PERSON>,dessin,ç<PERSON><PERSON>,zei<PERSON><PERSON>,mengg<PERSON><PERSON>,rys<PERSON><PERSON>,vẽ,kres<PERSON><PERSON>,วาดรูป,desen<PERSON>,raj<PERSON><PERSON><PERSON>,tekenen,kreslen<PERSON>,그리기,dibujar,рисуване,draw,disegnare,desen<PERSON>,dessin<PERSON>,dibuja,desen<PERSON>o,рисунок,kreslen<PERSON>,disegni,рисунки,zeichnung", "onBoarding": true}, {"Interest": "animation", "SimilarInterests": "animación,animação,анимация,animazione,animasi,animasyon,animacja,الرسوم_المتحركة,animatie,แอนิเมชั่น,anim<PERSON><PERSON><PERSON>,анімація,animace,animacao,animaciones", "onBoarding": true}, {"Interest": "boardgames", "SimilarInterests": "j<PERSON><PERSON><PERSON><PERSON><PERSON>,настольныеигры,jeuxdes<PERSON>iété,gry<PERSON>lanszowe,deskovky,társasj<PERSON>tékok,настільніігри,บอร์ดเกม,lautapelit", "onBoarding": true}, {"Interest": "languages", "SimilarInterests": "idiomas,языки,langues,bahasa,lingue,sprachen,języki,اللغات,ngônngữ,jazyky,ภาษา,언어,nye<PERSON><PERSON>,زبانها,language,言語,语言,語言,languaes,lenguas", "onBoarding": true}, {"Interest": "divertido", "SimilarInterests": "diversão,zábavné,fun,sjov,spass,spaß,diversión,zabavne", "onBoarding": false}, {"Interest": "writing", "SimilarInterests": "escritura,escrever,писательство,schreiben,écriture,scrittura,pisanie,الكتابة,psaní,viết,نوشتن,письменництво,írás,schrijven,การเขียน,כתיבה,γράψιμο,escrita,글쓰기,písanie,escribir,寫作,pisanje,წერა,write,ecriture,yazı,yazmak,escreve,كتابة,escrevendo,escritos,writings", "onBoarding": true}, {"Interest": "crime", "SimilarInterests": "crimen,crimine,الجريمة,kejahatan,อาชญากรรม,έγκλημα,krimi,kryminał,polisiye", "onBoarding": true}, {"Interest": "universe", "SimilarInterests": "universo,вселенная,universum,evren,wszechświat,univers,vesm<PERSON><PERSON>,vũ<PERSON><PERSON>,всесвіт,univerzum,宇宙,σύμπαν,จักรวาล,universet,вселена,alamsemesta,universes,universos,الكون,theuniverse", "onBoarding": true}, {"Interest": "philosophy", "SimilarInterests": "filosofía,filosofia,философия,felsefe,philosophie,filosofi,filozofia,filozofie,الفلسفة,філософія,哲学,φιλοσοφία,triế<PERSON><PERSON>,فلسفه,filo<PERSON><PERSON><PERSON>,ปรัชญา,פילוסופיה,철학,filosofie,哲學,filozofija,philosophical,филослфия,philosopy,philosophia,philosofí", "onBoarding": true}, {"Interest": "museums", "SimilarInterests": "museos,музеи,museus,musei,musées,mü<PERSON><PERSON>,museum,museen,muzee,музеї,museo,музей", "onBoarding": true}, {"Interest": "gadgets", "SimilarInterests": "гаджеты,gadżety,гаджети", "onBoarding": true}, {"Interest": "longtermrelationship", "SimilarInterests": "долговременныеотношения,lang<PERSON><PERSON><PERSON><PERSON>hung,u<PERSON>n<PERSON>liiliş<PERSON>,relazioneserialungotermine,ความสัมพันธ์ระยะยาว,hossz<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>t,d<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,d<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,довгостроковістосунки,راب<PERSON>ه<PERSON><PERSON>ندمدت,quanh<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,rela<PERSON><PERSON>peter<PERSON>,radydotyczącezwiązku,vztahovérady,relaciónsería,отношегия", "onBoarding": false}, {"Interest": "friends", "SimilarInterests": "amigos,друзья,amis,amici,freunde,arka<PERSON><PERSON><PERSON>,เพื่อน,أصدقاء,prieteni,دوستان,p<PERSON><PERSON><PERSON><PERSON>,друзі,barátok,vrienden,友達,amiga<PERSON>,z<PERSON><PERSON><PERSON>,arka<PERSON>,k<PERSON><PERSON><PERSON><PERSON>,te<PERSON>,친구,friend,amigo", "onBoarding": false}, {"Interest": "baking", "SimilarInterests": "repostería,выпечка,backen,pie<PERSON><PERSON><PERSON>,s<PERSON><PERSON><PERSON>,pe<PERSON><PERSON><PERSON>,烘焙,hornear", "onBoarding": true}, {"Interest": "gym", "SimilarInterests": "gim<PERSON><PERSON>,si<PERSON><PERSON><PERSON>,si<PERSON><PERSON>,fitness<PERSON>er,<PERSON><PERSON><PERSON><PERSON>,γυμν<PERSON><PERSON>τ<PERSON><PERSON><PERSON><PERSON>,חד<PERSON><PERSON><PERSON><PERSON><PERSON>,健身房,ジム,gymlife,спортзал,salledesport,edz<PERSON><PERSON>em,posilovna", "onBoarding": true}, {"Interest": "meditation", "SimilarInterests": "meditacion,медитация,meditação,méditation,meditazione,meditasyon,medytacja,meditasi,التأمل,meditatie,meditace,thi<PERSON>n<PERSON><PERSON><PERSON>,冥想,medit<PERSON><PERSON><PERSON>,медитація,瞑想,meditación,meditate", "onBoarding": true}, {"Interest": "football", "SimilarInterests": "fußball,fotbal,bóng<PERSON><PERSON>,football️,fotball,fútbol,futebol,futbol,футбол,piłkanoż<PERSON>,soccer,voetbal,كرة_القدم,ποδόσφαιρο,calcio,כדורגל,futbal,fussball,fudbal,bola,sepakbola", "onBoarding": true}, {"Interest": "relationshipadvice", "SimilarInterests": "consejopararelaciones,consel<PERSON><PERSON><PERSON><PERSON>,il<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,советыпоотношениям,na<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,lờikhuyênchomốiq<PERSON>ệ,conseilsrelationnels,نصائح_العلاقات,consiglisul<PERSON><PERSON>zioni,beziehun<PERSON><PERSON><PERSON><PERSON>,คำแนะนำด้านความสัมพันธ์,توصیهرابطه,관계조언,关系建议,עצותמערכותיחסים,συμβουλέςσχέσεων,ka<PERSON><PERSON><PERSON><PERSON>,付き合いのアドバイス,relatieadvies", "onBoarding": true}, {"Interest": "shortterm", "SimilarInterests": "curtop<PERSON>o,kurz<PERSON><PERSON>ig,ระยะสั้น", "onBoarding": false}, {"Interest": "astrology", "SimilarInterests": "astrología,astrologia,астрология,astrologie,astroloji,astrologi,asztrológia,ستارهشناسی,астрологія", "onBoarding": true}, {"Interest": "casual", "SimilarInterests": "occasionale,zwang<PERSON>,nezavazne", "onBoarding": false}, {"Interest": "physics", "SimilarInterests": "física,fizik,физика,fisi<PERSON>,physik,fizyka,fisika,fyzika", "onBoarding": true}, {"Interest": "fitness", "SimilarInterests": "physique,фитнес,健身,اللياقة_البدنية,כושר,фітнес,フィットネス,fitnesas", "onBoarding": true}, {"Interest": "scifi", "SimilarInterests": "bilim<PERSON><PERSON><PERSON>,cienciaficcion,fantascienza,sciencefiction,научнаяфантастика", "onBoarding": true}, {"Interest": "minecraft", "SimilarInterests": "майнкра<PERSON>т", "onBoarding": true}, {"Interest": "festivals", "SimilarInterests": "festivales,festival,festivais,фестивали,festiwale,festivaly,fesztiválok", "onBoarding": true}, {"Interest": "sports", "SimilarInterests": "sport,deportes,спорт,esportes,olahraga,sporlar,thểthao,sporturi,športy,ספורט,الرياضات,スポーツ,sportas,esporte,sporr,spor", "onBoarding": true}, {"Interest": "pokemon", "SimilarInterests": "<PERSON><PERSON><PERSON><PERSON>,ポケモン", "onBoarding": true}, {"Interest": "kpop", "SimilarInterests": "кпоп", "onBoarding": true}, {"Interest": "partying", "SimilarInterests": "party,fête,вечеринки,petrecere,feesten,پارتی,fiesta,parties,fete,defiesta,več<PERSON>rky,rumba,festejar,berpesta,celebration,feste,celebrate,impreza,fetes,celebrations,celebracion,comemorações,festejos", "onBoarding": true}, {"Interest": "standup", "SimilarInterests": "стэндап,стендап", "onBoarding": true}, {"Interest": "indie", "SimilarInterests": "инди", "onBoarding": true}, {"Interest": "libra", "SimilarInterests": "весы,terazi,bilancia,thi<PERSON><PERSON><PERSON><PERSON><PERSON>,برج_الميزان", "onBoarding": true}, {"Interest": "jazz", "SimilarInterests": "д<PERSON><PERSON><PERSON>", "onBoarding": true}, {"Interest": "country", "SimilarInterests": "pais", "onBoarding": true}, {"Interest": "mystery", "SimilarInterests": "misterio,mist<PERSON><PERSON>,mistero,myst<PERSON>,gizem,misteri,الغموض", "onBoarding": true}, {"Interest": "virgo", "SimilarInterests": "дева,başak,برج_العذراء", "onBoarding": true}, {"Interest": "punk", "SimilarInterests": "панк", "onBoarding": true}, {"Interest": "design", "SimilarInterests": "diseño,disegno,дизайн,tasar<PERSON>m,projekt<PERSON>nie,desain,desen,التصميم,dizayn,designing,desing", "onBoarding": true}, {"Interest": "documentaries", "SimilarInterests": "documentales,documentários,documentaires,документальныефильмы,documentari,documentary", "onBoarding": true}, {"Interest": "cosplay", "SimilarInterests": "косплей,コスプレ", "onBoarding": true}, {"Interest": "theater", "SimilarInterests": "teatro,театр,tiyatro,th<PERSON><PERSON><PERSON>,divadlo,teater,theatre", "onBoarding": true}, {"Interest": "dungeonsanddragons", "SimilarInterests": "подземельеидраконы,dungeonsidragons,dungeondragons", "onBoarding": true}, {"Interest": "filme", "SimilarInterests": "phim,ภาพยนตร์,영화,movie,filem", "onBoarding": false}, {"Interest": "dance", "SimilarInterests": "baile,dan<PERSON>,танец,tanzen,danse,taniec,dans,dancing,danza,tanec,menari,bailar,танці,dan<PERSON><PERSON>,dancas,baila,bailando", "onBoarding": true}, {"Interest": "juegos", "SimilarInterests": "игры,الألعاب,ігри,games,παιχνίδια,თამაშები,žaidimai", "onBoarding": true}, {"Interest": "<PERSON><PERSON><PERSON>", "SimilarInterests": "кдрама,drama<PERSON><PERSON><PERSON>,k<PERSON><PERSON>,k<PERSON><PERSON>,drama<PERSON><PERSON>,koreadrama,koreandrama", "onBoarding": true}, {"Interest": "basketball", "SimilarInterests": "baloncesto,basquete,basketbol,баскетбол,basketbal", "onBoarding": true}, {"Interest": "techno", "SimilarInterests": "техно,tekno", "onBoarding": true}, {"Interest": "drama", "SimilarInterests": "драма,dram,dorama,дорама,draama", "onBoarding": true}, {"Interest": "classical", "SimilarInterests": "классическая", "onBoarding": true}, {"Interest": "fashion", "SimilarInterests": "moda,мода,الموضة,móda,ファッション", "onBoarding": true}, {"Interest": "mythology", "SimilarInterests": "mitologia,mitología,mythologie,мифология,mitoloji", "onBoarding": true}, {"Interest": "humanrights", "SimilarInterests": "<PERSON><PERSON><PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON><PERSON>,права<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,dir<PERSON><PERSON><PERSON>,d<PERSON><PERSON><PERSON><PERSON><PERSON>,ins<PERSON><PERSON><PERSON>,ανθρώπιναδικαιώματα", "onBoarding": true}, {"Interest": "aries", "SimilarInterests": "<PERSON><PERSON>,овен,widder,ariete,b<PERSON><PERSON><PERSON><PERSON>", "onBoarding": true}, {"Interest": "painting", "SimilarInterests": "pintura,живопись,peinture,ma<PERSON>anie,pittura,ζωγρα<PERSON>ικ<PERSON>,ma<PERSON><PERSON><PERSON>,schilderen,male<PERSON>,ma<PERSON><PERSON><PERSON>,ma<PERSON><PERSON>,живопис,малювання,繪畫,צי<PERSON><PERSON>,fest<PERSON><PERSON><PERSON>,paintings", "onBoarding": true}, {"Interest": "hindu", "SimilarInterests": "hinduism", "onBoarding": false}, {"Interest": "hiking", "SimilarInterests": "senderismo,wandern,randon<PERSON><PERSON>,t<PERSON><PERSON><PERSON><PERSON><PERSON>,ха<PERSON><PERSON><PERSON><PERSON><PERSON>,hike,поход,prochaz<PERSON>,randon<PERSON>,hikes,escursioni,doğadayürüyüş,turistika,w<PERSON><PERSON><PERSON><PERSON>,doğ<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,drum<PERSON>i", "onBoarding": true}, {"Interest": "scorpio", "SimilarInterests": "escorpio,escorpi<PERSON>,скорпион,akrep,scorpione,عقرب,天蠍座,天蝎座", "onBoarding": true}, {"Interest": "gardening", "SimilarInterests": "jardiner<PERSON>,g<PERSON><PERSON><PERSON><PERSON>,jardinage,садоводство,berkebun,jardinagem,giardinaggio,kert<PERSON>zkedés,tuinieren,садівництво,garden,gartenarbeit,ogrodnictwo,jardineria,zahrada,ogród", "onBoarding": true}, {"Interest": "cancer", "SimilarInterests": "c<PERSON><PERSON>,рак,krebs,cancro", "onBoarding": true}, {"Interest": "crafts", "SimilarInterests": "ремесла,manualidades,artesanías,handicraft,crafting,craft,basteln,handarbeiten", "onBoarding": true}, {"Interest": "videoju<PERSON><PERSON>", "SimilarInterests": "jeuxvidéos,videogames,videohry,videojuegos,videojocs,видеоигры,jeuxvideos,jogosdecomputador,videjuuegos,jogarjogos", "onBoarding": true}, {"Interest": "badminton", "SimilarInterests": "bulutangkis", "onBoarding": true}, {"Interest": "electronic", "SimilarInterests": "elektronisch,elektroniczna,elektronická,eletronic,electrónica,eletrônica,électronique,elektronik,electronics", "onBoarding": true}, {"Interest": "swimming", "SimilarInterests": "nata<PERSON>,schwi<PERSON>n,плаван<PERSON>е,y<PERSON><PERSON><PERSON>,berenang,p<PERSON><PERSON><PERSON><PERSON>,natation,nata<PERSON>,nuoto,swim,nadar,y<PERSON><PERSON><PERSON><PERSON>,renang", "onBoarding": true}, {"Interest": "television", "SimilarInterests": "televisión,televisão,télévision,fernsehen,televisione,tv,telewizja", "onBoarding": true}, {"Interest": "chess", "SimilarInterests": "шахматы,satranç,<PERSON>checs,szachy,شطرنج", "onBoarding": true}, {"Interest": "poetry", "SimilarInterests": "poesía,poesia,поэзия,şiir,po<PERSON>ie,poezja,poesie,puisi,שירה,poezie,poem,شعر,poema,költ<PERSON><PERSON><PERSON>,поезія,poems,ποίηση,poesi,poet,poezija,стих,poetery,gedichte,wiersze,poemes,poesias,poeta,poeesia,стихотворения,şiirler,básn<PERSON>,вірш,poème,poezia,الشعر", "onBoarding": true}, {"Interest": "meme", "SimilarInterests": "มีม,模因,ミーム,меме", "onBoarding": false}, {"Interest": "relaciónseria", "SimilarInterests": "relacionamentosério,relations<PERSON><PERSON>,علاقة_جادة,seriousrelationship,relacionamentoserio,cid<PERSON><PERSON><PERSON><PERSON>,hubunganserius", "onBoarding": false}, {"Interest": "vegetarian", "SimilarInterests": "vegetariano,vegetarisch,vé<PERSON>tarien,vegetarián,vegetarianismo,вегетарианство", "onBoarding": true}, {"Interest": "capricorn", "SimilarInterests": "capric<PERSON>io,capric<PERSON><PERSON><PERSON>,козер<PERSON>г,capricorn<PERSON>,o<PERSON><PERSON>,capric<PERSON><PERSON>,mak<PERSON><PERSON>,mak", "onBoarding": true}, {"Interest": "gemini", "SimilarInterests": "g<PERSON><PERSON><PERSON>,близн<PERSON><PERSON><PERSON>,g<PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON><PERSON>,b<PERSON><PERSON><PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON>,geminis", "onBoarding": true}, {"Interest": "latino", "SimilarInterests": "hispanicamerican", "onBoarding": false}, {"Interest": "sagittarius", "SimilarInterests": "sagitario,sagit<PERSON><PERSON>,стре<PERSON><PERSON><PERSON>,sagittaire,sagittario,nhânmã", "onBoarding": true}, {"Interest": "taurus", "SimilarInterests": "tauro,теле<PERSON>,taure<PERSON>,kim<PERSON><PERSON><PERSON>,برج_الثور", "onBoarding": true}, {"Interest": "aquarius", "SimilarInterests": "<PERSON><PERSON><PERSON><PERSON><PERSON>,во<PERSON><PERSON><PERSON><PERSON><PERSON>,<PERSON><PERSON>,<PERSON><PERSON><PERSON>,acquario", "onBoarding": true}, {"Interest": "pisces", "SimilarInterests": "piscis,song<PERSON><PERSON>,برج_الحوت", "onBoarding": true}, {"Interest": "fortnite", "SimilarInterests": "fornite", "onBoarding": true}, {"Interest": "cuisine", "SimilarInterests": "foodculture,μαγειρική", "onBoarding": false}, {"Interest": "lgbtqally", "SimilarInterests": "lgbt,lgbtq,лгбт,lgbtally,lgbtqa,aliadelgbtq,alliélgbtq", "onBoarding": true}, {"Interest": "lucu", "SimilarInterests": "cute,kawaii", "onBoarding": false}, {"Interest": "romantis", "SimilarInterests": "رومانسي,romantici,lã<PERSON><PERSON><PERSON>n,رمانتیک,romantikus,romantic,romantique,romântico,romántico", "onBoarding": false}, {"Interest": "environmentalism", "SimilarInterests": "ambientalismo,çevrecilik", "onBoarding": true}, {"Interest": "boxing", "SimilarInterests": "boxeo,boxe,бокс,boxen", "onBoarding": true}, {"Interest": "folk", "SimilarInterests": "фолк", "onBoarding": true}, {"Interest": "yoga", "SimilarInterests": "йога,ioga,joga", "onBoarding": true}, {"Interest": "feminism", "SimilarInterests": "feminismo,feminismus,féminisme,феминизм,femminismo,feminizm,feminisme,feminist", "onBoarding": true}, {"Interest": "komik", "SimilarInterests": "comics,comic,fumetti,quadrinhos,комиксы,komiks,peliculascomicas", "onBoarding": false}, {"Interest": "beauty", "SimilarInterests": "belleza,красота,beleza,beauté,pi<PERSON><PERSON><PERSON>,gü<PERSON><PERSON>,bellezza,الجمال,krása,frumusete,краса,beaute,belezza,güzelik", "onBoarding": true}, {"Interest": "christian", "SimilarInterests": "c<PERSON><PERSON>,ch<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,christ<PERSON>,k<PERSON><PERSON><PERSON><PERSON>,christians,христианство,christianity,cristianism,cristianesimo", "onBoarding": false}, {"Interest": "running", "SimilarInterests": "correr,corrida,бег,berl<PERSON>,laufen,bi<PERSON><PERSON>,<PERSON><PERSON><PERSON><PERSON>,ko<PERSON><PERSON>,b<PERSON>h,futás,τρέξιμο", "onBoarding": true}, {"Interest": "conspiracytheories", "SimilarInterests": "teoriasconspirativas,teoriasdaconspiração,теориизаговора,verschwörungstheorien,teorikonspirasi,teoriecospirative,théoriesducomplot", "onBoarding": true}, {"Interest": "volleyball", "SimilarInterests": "voleibol,vôlei,волейбол,bolavoli,voleybol,pallavolo,volei,volleybal,volleball", "onBoarding": true}, {"Interest": "cycling", "SimilarInterests": "ciclismo,bersepeda,cyclisme,cyklistika,kerékpá<PERSON>,biking,велоспорт,ciclism", "onBoarding": true}, {"Interest": "showerthoughts", "SimilarInterests": "pensamientosenladucha,pensamentosdechuveiro,pen<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,dusch<PERSON><PERSON>ken,réfle<PERSON>ons<PERSON><PERSON><PERSON><PERSON>,myś<PERSON><PERSON>sznicowe,pikiransaatmandi,duş<PERSON><PERSON>şünceleri,myšlenkyzesprchy,pensarenladucha", "onBoarding": true}, {"Interest": "video", "SimilarInterests": "видео,відео,วีดีโอ,βίντεο,视频,vidéo", "onBoarding": false}, {"Interest": "martialarts", "SimilarInterests": "artesmarciales,artesmarciais,боевыеискусства,dövüşsanatları,artimarziali,artsmartiaux", "onBoarding": true}, {"Interest": "makeup", "SimilarInterests": "maquillaje,мак<PERSON><PERSON><PERSON>,maqui<PERSON><PERSON>,maki<PERSON><PERSON>,maquillage,makyaj,sminkel<PERSON>", "onBoarding": true}, {"Interest": "weightlifting", "SimilarInterests": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON><PERSON><PERSON>,тяжелаяатлетика,podnoszenieciężarów,musculação,musculation", "onBoarding": true}, {"Interest": "latin", "SimilarInterests": "latina,latinas", "onBoarding": true}, {"Interest": "filmmaking", "SimilarInterests": "filmyapımı", "onBoarding": true}, {"Interest": "<PERSON><PERSON><PERSON><PERSON>", "SimilarInterests": "فكاهة,humor,humour,mizah,humours,طنز", "onBoarding": false}, {"Interest": "terror", "SimilarInterests": "τρόμος", "onBoarding": false}, {"Interest": "volunteering", "SimilarInterests": "voluntariado,волонтерство,bénévolat,volontariato,freiwillige<PERSON><PERSON>t,<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON><PERSON><PERSON>,volunteer", "onBoarding": true}, {"Interest": "lifeadvice", "SimilarInterests": "conselhodevida,conseils<PERSON><PERSON>,leven<PERSON><PERSON>,עצותלחיים,consejodevida,consiglidi<PERSON>ta,nasi<PERSON><PERSON><PERSON><PERSON>,leben<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,hayatt<PERSON><PERSON><PERSON><PERSON>,frasesdevida,<PERSON>on", "onBoarding": true}, {"Interest": "birds", "SimilarInterests": "aves,p<PERSON><PERSON><PERSON>,ku<PERSON><PERSON>,птицы,oiseaux,vögel,ptaki,madarak,птахи,passaros,pajaros,burung,bird", "onBoarding": true}, {"Interest": "muslim", "SimilarInterests": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,مسلم", "onBoarding": false}, {"Interest": "baseball", "SimilarInterests": "béisbol,beisbol", "onBoarding": true}, {"Interest": "politics", "SimilarInterests": "política,politik,политика,politica,polityka,politique,siyaset,politika,політика,political", "onBoarding": true}, {"Interest": "coffee", "SimilarInterests": "café,kaffee,kahve,káv<PERSON>,káva,caffe,kopi,kaffe,cafea,caffè,cafe,カフェ,кофейни,cafeterias", "onBoarding": false}, {"Interest": "transally", "SimilarInterests": "aliadetrans", "onBoarding": true}, {"Interest": "cat<PERSON><PERSON><PERSON>", "SimilarInterests": "catholic,católico,katholisch,catolicismo,catholicism,catolica,católicos", "onBoarding": false}, {"Interest": "tennis", "SimilarInterests": "tenis,теннис,tênis", "onBoarding": true}, {"Interest": "skateboarding", "SimilarInterests": "skateboard,skateboarden,скейтбординг", "onBoarding": true}, {"Interest": "fish", "SimilarInterests": "peixes,рыбы,pescado,poisson,fische,balık,ikan,ปลา", "onBoarding": true}, {"Interest": "debates", "SimilarInterests": "d<PERSON>bats,di<PERSON><PERSON><PERSON>,debat,debaty,debatten,debater,debate", "onBoarding": true}, {"Interest": "одинокий", "SimilarInterests": "lonely,เหงา,самотня,одиночество,solidão,kesepian,einsamkeit", "onBoarding": false}, {"Interest": "hockey", "SimilarInterests": "хоккей,hokej,nhl", "onBoarding": true}, {"Interest": "alimentation", "SimilarInterests": "nutrition,nutrición", "onBoarding": false}, {"Interest": "amour", "SimilarInterests": "love,amor,αγάπη,amours,liebe,cinta,mił<PERSON><PERSON><PERSON>,любвь,amores,láska,amo,i<PERSON><PERSON>,s<PERSON><PERSON><PERSON>,eros,dragoste,amar,אהב<PERSON>,любоф,milos<PERSON>,t<PERSON><PERSON><PERSON><PERSON>,ความรัก,a<PERSON>ko,sevgı", "onBoarding": false}, {"Interest": "agnostic", "SimilarInterests": "a<PERSON><PERSON><PERSON><PERSON>", "onBoarding": false}, {"Interest": "golf", "SimilarInterests": "golfing", "onBoarding": true}, {"Interest": "worldnews", "SimilarInterests": "notici<PERSON><PERSON><PERSON><PERSON>,мировыеновости,notíciasmundiais,beritadunia,newsdalmondo", "onBoarding": true}, {"Interest": "sex", "SimilarInterests": "секса", "onBoarding": false}, {"Interest": "realitytv", "SimilarInterests": "realityshow", "onBoarding": true}, {"Interest": "european", "SimilarInterests": "europeo,europeu", "onBoarding": false}, {"Interest": "nature", "SimilarInterests": "природа,naturaleza,natureza,priroda,przyroda,természet,luonto,ธรรมชาติ", "onBoarding": false}, {"Interest": "pingpong", "SimilarInterests": "пингпонг", "onBoarding": true}, {"Interest": "crypto", "SimilarInterests": "cripto,krypto,kripto,крипто,cryptomonnaie,cryptocurrency,криптовалюта,criptomonedas", "onBoarding": true}, {"Interest": "korku", "SimilarInterests": "공포,miedo,fear,fears,peur", "onBoarding": false}, {"Interest": "news", "SimilarInterests": "noticias,новости,actualités,notícias,nachrichten,berita,haberler,новини,noticia", "onBoarding": true}, {"Interest": "snowboarding", "SimilarInterests": "сноуборд,snowboarden", "onBoarding": true}, {"Interest": "acuario", "SimilarInterests": "aquariums,aquarium", "onBoarding": false}, {"Interest": "vegan", "SimilarInterests": "vegano,veganismo,веганство,we<PERSON><PERSON>,веган", "onBoarding": true}, {"Interest": "músicaclásica", "SimilarInterests": "classicalmusic", "onBoarding": false}, {"Interest": "<PERSON><PERSON><PERSON>", "SimilarInterests": "flirting,flirt,paquera,coqueta", "onBoarding": false}, {"Interest": "cognitivefunctions", "SimilarInterests": "funcionescognitivas,когнитивныефункции,funçõescognitivas", "onBoarding": true}, {"Interest": "incontri", "SimilarInterests": "meetup,encontros,quedadas", "onBoarding": false}, {"Interest": "archaeology", "SimilarInterests": "arqueología,archeologia,arqueologia,археология,arkeoloji", "onBoarding": true}, {"Interest": "<PERSON><PERSON><PERSON><PERSON>", "SimilarInterests": "美<PERSON>,foodie,kuliner", "onBoarding": false}, {"Interest": "polyamory", "SimilarInterests": "poliamor,poliamore,polyamorie,polyamorous,poliamores,poliamorus", "onBoarding": false}, {"Interest": "araçlar", "SimilarInterests": "cars,car,samochody,carros,automobile,voiture,autók,carro,voitures,bilar,машини,vehicles", "onBoarding": false}, {"Interest": "поп", "SimilarInterests": "popmusic", "onBoarding": false}, {"Interest": "reading", "SimilarInterests": "lectura,독서,leer,lettura,c<PERSON><PERSON>e,ol<PERSON><PERSON>,membaca", "onBoarding": false}, {"Interest": "brasile<PERSON>", "SimilarInterests": "brazilian,brasileira", "onBoarding": false}, {"Interest": "spiritual", "SimilarInterests": "espiritual,spirituality,spirituelle,spirituell,spirituale,spirituális,spiritality,spiritualität,duchowość,spirituel,spirituelles,spiritualitás,espitualidade,espíritualidad", "onBoarding": false}, {"Interest": "skiing", "SimilarInterests": "schi<PERSON><PERSON>", "onBoarding": true}, {"Interest": "storia", "SimilarInterests": "story,cerita", "onBoarding": false}, {"Interest": "mbtimemes", "SimilarInterests": "mbtimeme", "onBoarding": true}, {"Interest": "<PERSON><PERSON>or", "SimilarInterests": "<PERSON><PERSON><PERSON>,c<PERSON><PERSON><PERSON><PERSON>,humornegro,schwar<PERSON><PERSON><PERSON>", "onBoarding": false}, {"Interest": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SimilarInterests": "sarcasm,sarcasmo", "onBoarding": false}, {"Interest": "astrologymemes", "SimilarInterests": "memesdeastrología,мемыастрологии", "onBoarding": true}, {"Interest": "bisexual", "SimilarInterests": "bissexual,bisexualgirl", "onBoarding": false}, {"Interest": "tattoos", "SimilarInterests": "tattoo,tatouage,tatu<PERSON><PERSON>,tatu<PERSON><PERSON><PERSON>,tatu<PERSON><PERSON>,tattooed,tetov<PERSON>,d<PERSON><PERSON><PERSON><PERSON>,tatu<PERSON>,tatuar,tatu<PERSON><PERSON>,tat2,tatom,татуироки,tattooart", "onBoarding": false}, {"Interest": "camping", "SimilarInterests": "acampar,kemping,campamento,campinglife", "onBoarding": false}, {"Interest": "enneagram", "SimilarInterests": "eneagrama,эннеаграмма", "onBoarding": true}, {"Interest": "confeitaria", "SimilarInterests": "pasticceria,pastelería,pastelaria", "onBoarding": false}, {"Interest": "gymnastics", "SimilarInterests": "gimnasia,гимнастика,ginástica,ginnastica", "onBoarding": true}, {"Interest": "scubadiving", "SimilarInterests": "plongéesousmarine,nurkowanie,tüplüdalış,scuba", "onBoarding": true}, {"Interest": "handwerk", "SimilarInterests": "artisanat,artigianato,rzemiosło,kerajinan,hantverk", "onBoarding": false}, {"Interest": "leão", "SimilarInterests": "ле<PERSON>,aslan,lion,leone,s<PERSON>tử", "onBoarding": false}, {"Interest": "surfing", "SimilarInterests": "surf,surfe,surfen", "onBoarding": true}, {"Interest": "adventure", "SimilarInterests": "aventura,przygoda,macera,adventures,rolês", "onBoarding": false}, {"Interest": "cuddling", "SimilarInterests": "cuddles,cuddle,kuscheln,cuddlepuddle,carinhos", "onBoarding": false}, {"Interest": "santai", "SimilarInterests": "relaxation,relajacion,détente,rahatlamak", "onBoarding": false}, {"Interest": "virgem", "SimilarInterests": "vergine,szűz,virgin", "onBoarding": false}, {"Interest": "mentalhealth", "SimilarInterests": "mentalhealthawareness,saúdemental,santementale,saludmente", "onBoarding": false}, {"Interest": "touro", "SimilarInterests": "boğa,toro", "onBoarding": false}, {"Interest": "beach", "SimilarInterests": "playa,praia,beaches,plage,praias,spiaggia,παραλία,plaj", "onBoarding": false}, {"Interest": "italiano", "SimilarInterests": "italian", "onBoarding": false}, {"Interest": "klasik", "SimilarInterests": "clássi<PERSON>,classique,كلاسيكي,classic,классика", "onBoarding": false}, {"Interest": "zawieranieprzyjaźni", "SimilarInterests": "conoceramigos,makingfriends,makefriends,haceramigos,fazeramigos,k<PERSON><PERSON><PERSON><PERSON>n", "onBoarding": false}, {"Interest": "мистика", "SimilarInterests": "mysticism", "onBoarding": false}, {"Interest": "deepconversation", "SimilarInterests": "deepconversations,conversacionprofunda,platicasprofundas", "onBoarding": false}, {"Interest": "велосипед", "SimilarInterests": "bike,rowery,bikes,bicycle,fahrrad,bicicletta,vélo,bisiklet", "onBoarding": false}, {"Interest": "повседневный", "SimilarInterests": "daily,quotidien,günlük,everyday,повседневно,mindennapok,каждыйдень", "onBoarding": false}, {"Interest": "series", "SimilarInterests": "soro<PERSON>t", "onBoarding": false}, {"Interest": "sleep", "SimilarInterests": "sleeping,dormir,tidur,dormire,sp<PERSON><PERSON>,spanek,sommeil,spanie", "onBoarding": false}, {"Interest": "электроннаямузыка", "SimilarInterests": "elettronica,електроннамузика,electronicmusic,electronica,musicaeletronica,músicaelectronica", "onBoarding": false}, {"Interest": "starwars", "SimilarInterests": "g<PERSON><PERSON><PERSON><PERSON><PERSON>jn<PERSON>", "onBoarding": false}, {"Interest": "introvert", "SimilarInterests": "introverts,introverted,introvertiert,introvers,introvertida,introversión", "onBoarding": false}, {"Interest": "polak", "SimilarInterests": "pole", "onBoarding": false}, {"Interest": "русский", "SimilarInterests": "russian,русское", "onBoarding": false}, {"Interest": "guitar", "SimilarInterests": "guitarra,gitara,gitarre,chitarra,guitars", "onBoarding": false}, {"Interest": "languagelearning", "SimilarInterests": "言語学習", "onBoarding": false}, {"Interest": "english", "SimilarInterests": "ingles,inglés", "onBoarding": false}, {"Interest": "pets", "SimilarInterests": "<PERSON><PERSON>,z<PERSON><PERSON><PERSON>,haustiere,pet,питомец", "onBoarding": false}, {"Interest": "relationship", "SimilarInterests": "relacionamento,отношения,relationships,relaciones,vztahy,relacje,zwiazki,hubungan,zwi<PERSON>z<PERSON>,kap<PERSON><PERSON><PERSON>,rapporti,відносин,ความสัมพันธ์", "onBoarding": false}, {"Interest": "skorpion", "SimilarInterests": "scorpion", "onBoarding": false}, {"Interest": "mode", "SimilarInterests": "style,estilo,стиль,stile", "onBoarding": false}, {"Interest": "fishing", "SimilarInterests": "pesca,wędkarstwo,horgászat,mancing,pêcher,fishings", "onBoarding": false}, {"Interest": "pansexual", "SimilarInterests": "пансексуал", "onBoarding": false}, {"Interest": "สบายๆ", "SimilarInterests": "algotranqui", "onBoarding": false}, {"Interest": "buddhist", "SimilarInterests": "буддизм,buddhism,budismo", "onBoarding": false}, {"Interest": "кулінарія", "SimilarInterests": "culinaria,culinary", "onBoarding": false}, {"Interest": "walking", "SimilarInterests": "walk,walks,spaziergänge,caminata,passeggiare,y<PERSON>r<PERSON>mek,caminhar,прогулянка,jalan,caminhada,caminatas,прогулканаприроде,camminate", "onBoarding": false}, {"Interest": "selfimprovement", "SimilarInterests": "superacionpersonal", "onBoarding": false}, {"Interest": "filipino", "SimilarInterests": "pinoy", "onBoarding": false}, {"Interest": "cartoons", "SimilarInterests": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "onBoarding": false}, {"Interest": "français", "SimilarInterests": "french,francais", "onBoarding": false}, {"Interest": "openminded", "SimilarInterests": "menteaberta", "onBoarding": false}, {"Interest": "filmer", "SimilarInterests": "filmmaker,кинопроизводство,filmproduktion", "onBoarding": false}, {"Interest": "workout", "SimilarInterests": "se<PERSON>,tre<PERSON>,ed<PERSON><PERSON>,workouts,ออกกำลังกาย,тренировка", "onBoarding": false}, {"Interest": "astronomy", "SimilarInterests": "astronomia", "onBoarding": false}, {"Interest": "weed", "SimilarInterests": "cannabis,marijuana", "onBoarding": false}, {"Interest": "deutsch", "SimilarInterests": "german", "onBoarding": false}, {"Interest": "balance", "SimilarInterests": "الميزان,kilter", "onBoarding": false}, {"Interest": "life", "SimilarInterests": "vida,vita,kehidupan,жизни,زندگی,ชีวิต,життє", "onBoarding": false}, {"Interest": "family", "SimilarInterests": "familie,famille,família,famile,familias,rodina,сімʼя", "onBoarding": false}, {"Interest": "space", "SimilarInterests": "spazio", "onBoarding": false}, {"Interest": "rpg", "SimilarInterests": "rpgs", "onBoarding": true}, {"Interest": "español", "SimilarInterests": "spanish", "onBoarding": false}, {"Interest": "cinema", "SimilarInterests": "cinéma,кино", "onBoarding": false}, {"Interest": "halloween", "SimilarInterests": "halloweeen,hellowen,хэллуин", "onBoarding": false}, {"Interest": "goth", "SimilarInterests": "góticas", "onBoarding": false}, {"Interest": "magyar", "SimilarInterests": "hungarian", "onBoarding": false}, {"Interest": "paranormal", "SimilarInterests": "paranormales", "onBoarding": false}, {"Interest": "programming", "SimilarInterests": "programar,programowanie,програмирование,programmation", "onBoarding": false}, {"Interest": "ambiente", "SimilarInterests": "environment,meioambiente", "onBoarding": false}, {"Interest": "หนังสยองขวัญ", "SimilarInterests": "horrormovies,scarymovies,películasdeterror,peliculasterror,horrorfilms,horrorfilme,filmesterror", "onBoarding": false}, {"Interest": "motos", "SimilarInterests": "motorcycle,motorcycles,motocykle,motarrad,motorky,motorad,motori,мотоцкл,motorsikletler,motociclete,motorka,motorcycleride,motocyclette,мотоезда,viagemdemoto,viaggiinmoto", "onBoarding": true}, {"Interest": "écologie", "SimilarInterests": "ecology,ecología", "onBoarding": false}, {"Interest": "indigenous", "SimilarInterests": "indígena,indigena", "onBoarding": false}, {"Interest": "türk", "SimilarInterests": "turkish", "onBoarding": false}, {"Interest": "kink", "SimilarInterests": "kinks", "onBoarding": false}, {"Interest": "sporty", "SimilarInterests": "активныйобразжизни", "onBoarding": false}, {"Interest": "cinématographie", "SimilarInterests": "cinematography", "onBoarding": false}, {"Interest": "buceo", "SimilarInterests": "tauchen,дайвинг,diving,dive", "onBoarding": false}, {"Interest": "friendship", "SimilarInterests": "交友,amizade,amistad,amicizia,amizades,amistades,friendships,дружба,amitié,przy<PERSON><PERSON><PERSON>,přátelství,frendship,arkadaslik,dostluk,vänskap,frind,amigar", "onBoarding": false}, {"Interest": "pcgaming", "SimilarInterests": "videojuegospc", "onBoarding": true}, {"Interest": "truecrime", "SimilarInterests": "truecrimes", "onBoarding": false}, {"Interest": "japan", "SimilarInterests": "japon,giappone,jepang,япония,japão", "onBoarding": false}, {"Interest": "piercings", "SimilarInterests": "piercing,pierced,пирсинг", "onBoarding": false}, {"Interest": "ترسناک", "SimilarInterests": "scary", "onBoarding": false}, {"Interest": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SimilarInterests": "mystical", "onBoarding": false}, {"Interest": "tamil", "SimilarInterests": "tamilan", "onBoarding": false}, {"Interest": "piano", "SimilarInterests": "pianoplaying", "onBoarding": false}, {"Interest": "divorced", "SimilarInterests": "divorce", "onBoarding": false}, {"Interest": "ngườiviệtnam", "SimilarInterests": "vietnamese", "onBoarding": false}, {"Interest": "asexual", "SimilarInterests": "asexuality", "onBoarding": false}, {"Interest": "work", "SimilarInterests": "working,praca,kerja,trab<PERSON><PERSON>,pr<PERSON><PERSON>,trabaja,trava<PERSON>,работаем,munka,lavorare,trabal<PERSON>o,werken,trabaho,i<PERSON><PERSON>", "onBoarding": false}, {"Interest": "kova", "SimilarInterests": "resilience", "onBoarding": false}, {"Interest": "creative", "SimilarInterests": "kreativ", "onBoarding": false}, {"Interest": "random", "SimilarInterests": "rando,aleatorio,aleat<PERSON><PERSON>,aleator<PERSON><PERSON>,aleat<PERSON><PERSON>s,aleat<PERSON><PERSON>s,aleatorios,randomly", "onBoarding": false}, {"Interest": "português", "SimilarInterests": "portuguese", "onBoarding": false}, {"Interest": "photo", "SimilarInterests": "foto,фото,fotos,pictures,photos,zdjecia,photes,photographs,poze,eufotos,fotosmias", "onBoarding": false}, {"Interest": "roadtrips", "SimilarInterests": "roadtrip,viagemdecarro,viajecarretera,carretera", "onBoarding": false}, {"Interest": "exercise", "SimilarInterests": "<PERSON><PERSON><PERSON><PERSON>,th<PERSON><PERSON><PERSON><PERSON>,exer<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,exercicio,lii<PERSON>nta", "onBoarding": false}, {"Interest": "climbing", "SimilarInterests": "arrampicata,escalade,escalada,wspinaczka", "onBoarding": false}, {"Interest": "ps5", "SimilarInterests": "playstation", "onBoarding": true}, {"Interest": "мысливдуше", "SimilarInterests": "думкивду<PERSON>і", "onBoarding": false}, {"Interest": "lesbian", "SimilarInterests": "lesbiana,lesbica", "onBoarding": false}, {"Interest": "manhwa", "SimilarInterests": "manhwas", "onBoarding": false}, {"Interest": "chill", "SimilarInterests": "pohoda,relajado", "onBoarding": false}, {"Interest": "auto", "SimilarInterests": "autos,motoryzacje,automotive,otomotif", "onBoarding": false}, {"Interest": "ислам", "SimilarInterests": "islam,islamic,الإسلام", "onBoarding": false}, {"Interest": "tea", "SimilarInterests": "chá,čaj", "onBoarding": false}, {"Interest": "selfcare", "SimilarInterests": "cuidadopersonal,autocuidados", "onBoarding": false}, {"Interest": "diy", "SimilarInterests": "barkácsolás,bricolage,faidate", "onBoarding": false}, {"Interest": "plants", "SimilarInterests": "ro<PERSON><PERSON><PERSON>,растения,piante", "onBoarding": false}, {"Interest": "українець", "SimilarInterests": "ukrainian", "onBoarding": false}, {"Interest": "tarot", "SimilarInterests": "tarocchi", "onBoarding": false}, {"Interest": "witchcraft", "SimilarInterests": "bruxaria,brujeria", "onBoarding": false}, {"Interest": "советыжизни", "SimilarInterests": "rady<PERSON><PERSON><PERSON>we,lifehacks", "onBoarding": false}, {"Interest": "آموزش", "SimilarInterests": "education", "onBoarding": false}, {"Interest": "kindness", "SimilarInterests": "kinder,доброта", "onBoarding": false}, {"Interest": "beer", "SimilarInterests": "cerveza,cerveja,biere,piwo,bières,beers,пивко", "onBoarding": false}, {"Interest": "xboxone", "SimilarInterests": "xbox", "onBoarding": true}, {"Interest": "wine", "SimilarInterests": "vino,vinho", "onBoarding": false}, {"Interest": "health", "SimilarInterests": "헬스,salud,zdrowie,kese<PERSON>an,здоровя", "onBoarding": false}, {"Interest": "trekking", "SimilarInterests": "trek", "onBoarding": false}, {"Interest": "sunset", "SimilarInterests": "atardecer,sunsets,son<PERSON><PERSON><PERSON>g<PERSON>nge,p<PERSON>rdosol,coucherdesoleil,günbatımı,zapadslunce,zachódsłońca,puestadesol", "onBoarding": false}, {"Interest": "adhd", "SimilarInterests": "adhdawareness", "onBoarding": false}, {"Interest": "darksouls", "SimilarInterests": "darksouls2", "onBoarding": true}, {"Interest": "thoughts", "SimilarInterests": "pensieri,pensamento,pensamiento,мысли,pensamientos,pensées,gedanke,gondolat,pensare,мы́сли", "onBoarding": false}, {"Interest": "nightowl", "SimilarInterests": "nightowls,nachtaktive,gecekusu,gececi", "onBoarding": false}, {"Interest": "japanese", "SimilarInterests": "日本人", "onBoarding": false}, {"Interest": "relaxing", "SimilarInterests": "relaxando", "onBoarding": false}, {"Interest": "علم", "SimilarInterests": "knowledge,conhecimentos,conoscenza,connaissances,poznanie,z<PERSON><PERSON><PERSON><PERSON><PERSON>", "onBoarding": false}, {"Interest": "supernatural", "SimilarInterests": "sobrenatural", "onBoarding": false}, {"Interest": "motivation", "SimilarInterests": "motivational,motivacao,motivations,мотивація,motivacio,motivazione,motywacja,motivatie,motivazionale", "onBoarding": false}, {"Interest": "星座", "SimilarInterests": "zodiac,zodiaco,burç,zodiak", "onBoarding": false}, {"Interest": "questionftheday", "SimilarInterests": "preguntadeldia,pytaniednia,preguntasdeldia,perguntasdodia,questionsdujours,questiondejour,preguntadia,v<PERSON><PERSON><PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,שאלהיומית", "onBoarding": false}, {"Interest": "webtoon", "SimilarInterests": "webtoons,webtooncomics", "onBoarding": false}, {"Interest": "selflove", "SimilarInterests": "amorpropio,autoamor,selbstliebe,amorpróprio", "onBoarding": false}, {"Interest": "motobike", "SimilarInterests": "motobikes", "onBoarding": false}, {"Interest": "conversation", "SimilarInterests": "conversa,rozmowa,conversaciones,разговор,comversa,discorsi,beszélgatés,conversaciónes", "onBoarding": false}, {"Interest": "rave", "SimilarInterests": "raves", "onBoarding": false}, {"Interest": "laughing", "SimilarInterests": "laughter,laugh,risate,risos", "onBoarding": false}, {"Interest": "driving", "SimilarInterests": "conduccion", "onBoarding": false}, {"Interest": "lego", "SimilarInterests": "legos", "onBoarding": false}, {"Interest": "lordoftherings", "SimilarInterests": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,se<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,seigneur<PERSON><PERSON>aux", "onBoarding": false}, {"Interest": "business", "SimilarInterests": "negocios,bisnis,negocio", "onBoarding": false}, {"Interest": "podcast", "SimilarInterests": "podcasts,podast", "onBoarding": false}, {"Interest": "depression", "SimilarInterests": "depressed,depores,depresivo,depressive", "onBoarding": false}, {"Interest": "mood", "SimilarInterests": "moods,心情,настрій,humeurs,настроении,inthemood", "onBoarding": false}, {"Interest": "money", "SimilarInterests": "dinheiro,dineros", "onBoarding": false}, {"Interest": "housemusik", "SimilarInterests": "housemusic", "onBoarding": false}, {"Interest": "جه<PERSON>", "SimilarInterests": "עולם,world,mundo,mondo", "onBoarding": false}, {"Interest": "roleplayer", "SimilarInterests": "roleplay,roleplaying,j<PERSON><PERSON><PERSON><PERSON>,j<PERSON><PERSON><PERSON><PERSON>,roleo", "onBoarding": true}, {"Interest": "mountains", "SimilarInterests": "mountain,montagna,montañas,montagne,gunung,munte,berge", "onBoarding": false}, {"Interest": "arab", "SimilarInterests": "عربي", "onBoarding": false}, {"Interest": "bored", "SimilarInterests": "gabut,aburridx,borad,aburrición,boredom,bosan,cansık<PERSON><PERSON><PERSON><PERSON>,jenuh,boring,tedioso,langweilig", "onBoarding": false}, {"Interest": "shopping", "SimilarInterests": "compras,sorunsal,shoppingsprees", "onBoarding": false}, {"Interest": "crochet", "SimilarInterests": "crocheting,s<PERSON><PERSON><PERSON><PERSON><PERSON>,cro<PERSON><PERSON>,<PERSON><PERSON><PERSON><PERSON>", "onBoarding": false}, {"Interest": "selfiee", "SimilarInterests": "selfiephoto,sefile", "onBoarding": false}, {"Interest": "math", "SimilarInterests": "mathematics,matematica,matematik", "onBoarding": false}, {"Interest": "korean", "SimilarInterests": "<PERSON><PERSON>", "onBoarding": false}, {"Interest": "literatura", "SimilarInterests": "literature,litteratur,letteratura,littérature", "onBoarding": false}, {"Interest": "moon", "SimilarInterests": "luna,луна", "onBoarding": false}, {"Interest": "nederlands", "SimilarInterests": "dutch", "onBoarding": false}, {"Interest": "posilování", "SimilarInterests": "empowerment", "onBoarding": false}, {"Interest": "architecture", "SimilarInterests": "arquitectura,architektura,архитеатура,architectural", "onBoarding": false}, {"Interest": "sexuality", "SimilarInterests": "szexualitás", "onBoarding": false}, {"Interest": "<PERSON><PERSON><PERSON>", "SimilarInterests": "natural", "onBoarding": false}, {"Interest": "talking", "SimilarInterests": "hablar,parler,ch<PERSON><PERSON>,kon<PERSON><PERSON><PERSON>,talkens", "onBoarding": false}, {"Interest": "jodoh", "SimilarInterests": "soulmates,soulmate,alma<PERSON><PERSON><PERSON><PERSON>,see<PERSON><PERSON><PERSON>ner", "onBoarding": false}, {"Interest": "finance", "SimilarInterests": "финансы,finances", "onBoarding": false}, {"Interest": "night", "SimilarInterests": "noche,noite,notte,ночь,malam,geceler,nights,nuite", "onBoarding": false}, {"Interest": "motorsport", "SimilarInterests": "motorsports,automovilismo", "onBoarding": false}, {"Interest": "bar", "SimilarInterests": "bars", "onBoarding": false}, {"Interest": "medieval", "SimilarInterests": "mittel<PERSON><PERSON>,médiévale", "onBoarding": false}, {"Interest": "tartışmalar", "SimilarInterests": "συζητήσεις,النقاشات,diskuse,discussion,discussions,обсуждения,discusión,discussione,diskuze", "onBoarding": false}, {"Interest": "gothic", "SimilarInterests": "gotico,goticos", "onBoarding": false}, {"Interest": "dinosaurs", "SimilarInterests": "dinosaurio,dinos", "onBoarding": false}, {"Interest": "hugs", "SimilarInterests": "abrazo<PERSON>,abra<PERSON><PERSON>,hugging,hug", "onBoarding": false}, {"Interest": "engineering", "SimilarInterests": "engineer,ingenieria,ingenier<PERSON>,ingeniera,ingeniero", "onBoarding": false}, {"Interest": "peace", "SimilarInterests": "paix,<PERSON><PERSON><PERSON>,paz<PERSON>or", "onBoarding": false}, {"Interest": "polish", "SimilarInterests": "polski", "onBoarding": false}, {"Interest": "newfriends", "SimilarInterests": "temanbaru,nouveauxamis", "onBoarding": false}, {"Interest": "quotes", "SimilarInterests": "cytaty,kata<PERSON>jak,quotations,citaty,citaçoes,quote,frase,s<PERSON>z,id<PERSON><PERSON>t,citate,citacao,alıntı,cytat,quoteoftheday,citationdujour,frasedeld<PERSON>,frasesdodia,quotesoftheday,cytatdnia,citatulzilei", "onBoarding": false}, {"Interest": "sad", "SimilarInterests": "sedih,tristes,<PERSON><PERSON><PERSON><PERSON><PERSON>,sadness,tristesse,smutek,печаль,sadthoughts", "onBoarding": false}, {"Interest": "motorradfahren", "SimilarInterests": "motociclismo,мотоциклизм,motorcycling", "onBoarding": false}, {"Interest": "airsoft", "SimilarInterests": "softair", "onBoarding": false}, {"Interest": "sala", "SimilarInterests": "room,habitacion", "onBoarding": false}, {"Interest": "rain", "SimilarInterests": "lluvia,hujan,raining,pioggia,pluie,rainy", "onBoarding": false}, {"Interest": "jokes", "SimilarInterests": "chistes,vtipy,жарти,анекдоти,joke,blague,broma,zoeira,barzel<PERSON>a,vtip,sarcasticjokes,sexjokes,dadjokes", "onBoarding": false}, {"Interest": "chatting", "SimilarInterests": "chats️,platicar,chaatt,chiac<PERSON><PERSON>re", "onBoarding": false}, {"Interest": "social", "SimilarInterests": "soziales,sociale", "onBoarding": false}, {"Interest": "military", "SimilarInterests": "army,militares", "onBoarding": false}, {"Interest": "morning", "SimilarInterests": "pagi,sabah,poran<PERSON>,rano,mattina,r<PERSON>o,mattino,reggeli", "onBoarding": false}, {"Interest": "skate", "SimilarInterests": "skating,patinage", "onBoarding": false}, {"Interest": "medicine", "SimilarInterests": "medycyna,медиццина,médecine", "onBoarding": false}, {"Interest": "relax", "SimilarInterests": "relaxare,відпочивати", "onBoarding": false}, {"Interest": "jesus", "SimilarInterests": "yeshua", "onBoarding": false}, {"Interest": "spacery", "SimilarInterests": "balade,pasear,promenade,passear,paseo,passeggiate,paseos,spacerowanie,plimbare,proch<PERSON>zka", "onBoarding": false}, {"Interest": "question01", "SimilarInterests": "questione,pergunta,domanda,d<PERSON><PERSON><PERSON>,k<PERSON><PERSON><PERSON>,ota<PERSON><PERSON>,ot<PERSON><PERSON><PERSON>,سوال,preguntas.questiones,pertanyaan,questãos,questionamentos,otazky,qwestions,intrebari,שאלות,ερωτησεις,dotazy,k<PERSON><PERSON><PERSON><PERSON>,questions1,questions01,questions001,question02,question2,question10,question101,question03,question10000", "onBoarding": false}, {"Interest": "weekend", "SimilarInterests": "findes,vikend,wochende,haftasonu", "onBoarding": false}, {"Interest": "religion", "SimilarInterests": "religious,religions,религии,religión,religiones,agama", "onBoarding": false}, {"Interest": "biology", "SimilarInterests": "biología,биология", "onBoarding": false}, {"Interest": "drinking", "SimilarInterests": "drink,bebida", "onBoarding": false}, {"Interest": "archery", "SimilarInterests": "bogenschießen", "onBoarding": false}, {"Interest": "sea", "SimilarInterests": "ocean,mare,mar,morze,θαλασσα", "onBoarding": false}, {"Interest": "motoadventure", "SimilarInterests": "motoaventura", "onBoarding": false}, {"Interest": "aviation", "SimilarInterests": "aviación,havacılık,авиа,avation", "onBoarding": false}, {"Interest": "r<PERSON><PERSON><PERSON><PERSON>", "SimilarInterests": "puzzles,zagadki", "onBoarding": true}, {"Interest": "destiny2", "SimilarInterests": "destiny", "onBoarding": true}, {"Interest": "hotchat", "SimilarInterests": "hotchats", "onBoarding": false}, {"Interest": "lol", "SimilarInterests": "хаха,mdr", "onBoarding": false}, {"Interest": "flowers", "SimilarInterests": "flower,flores,цветы,fleurs,kwiaty,flor,kv<PERSON><PERSON><PERSON>,k<PERSON><PERSON><PERSON>,çi<PERSON><PERSON>,blume,kytky,çicek,flori,květy", "onBoarding": false}, {"Interest": "kayak", "SimilarInterests": "kayaking", "onBoarding": false}, {"Interest": "guns", "SimilarInterests": "firearms,gun", "onBoarding": false}, {"Interest": "computers", "SimilarInterests": "computer,bilgisayar,computador", "onBoarding": false}, {"Interest": "randomthoughts", "SimilarInterests": "pensamento<PERSON><PERSON><PERSON><PERSON><PERSON>,randomthought", "onBoarding": false}, {"Interest": "editing", "SimilarInterests": "edicion", "onBoarding": false}, {"Interest": "weird", "SimilarInterests": "strange,странное", "onBoarding": false}, {"Interest": "straykids", "SimilarInterests": "skz", "onBoarding": false}, {"Interest": "norwegian", "SimilarInterests": "norsk", "onBoarding": false}, {"Interest": "<PERSON><PERSON><PERSON>", "SimilarInterests": "dramas", "onBoarding": false}, {"Interest": "korea", "SimilarInterests": "corea", "onBoarding": false}, {"Interest": "digitalart", "SimilarInterests": "artedigital", "onBoarding": false}, {"Interest": "research", "SimilarInterests": "pesquisa", "onBoarding": false}, {"Interest": "gundam", "SimilarInterests": "gundammodelkits", "onBoarding": true}, {"Interest": "jewish", "SimilarInterests": "judaism,judaísmo", "onBoarding": false}, {"Interest": "dreams", "SimilarInterests": "dream,sueños,dreaming,sonhos,reve,soñar,hayal", "onBoarding": false}, {"Interest": "aesthetic", "SimilarInterests": "aesthetics,estetika,эстет", "onBoarding": false}, {"Interest": "monsterhunter", "SimilarInterests": "monstehunter", "onBoarding": true}, {"Interest": "tvshows", "SimilarInterests": "tvshow,seriad<PERSON>,se<PERSON><PERSON><PERSON>,seriado", "onBoarding": false}, {"Interest": "calistenia", "SimilarInterests": "calisthenics,calisthenic", "onBoarding": false}, {"Interest": "thrifting", "SimilarInterests": "thrift", "onBoarding": false}, {"Interest": "gastronomia", "SimilarInterests": "gastronomía", "onBoarding": false}, {"Interest": "massage", "SimilarInterests": "masaj", "onBoarding": false}, {"Interest": "socialanxiety", "SimilarInterests": "ansiedadesocial", "onBoarding": false}, {"Interest": "investing", "SimilarInterests": "investment,invest,inversiones", "onBoarding": false}, {"Interest": "homebody", "SimilarInterests": "домосед", "onBoarding": false}, {"Interest": "crystals", "SimilarInterests": "crystal", "onBoarding": false}, {"Interest": "selfdevelopment", "SimilarInterests": "samoroz<PERSON><PERSON><PERSON>,se<PERSON><PERSON><PERSON><PERSON>,se<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,خو<PERSON><PERSON><PERSON><PERSON><PERSON>,rozw<PERSON>josobisty,personaldevelopment,desarrollopersonal,persönlichkeitsentwic,développementpersonel,dezvoltarepersonala", "onBoarding": false}, {"Interest": "drums", "SimilarInterests": "drum,drumming,schlagzeug", "onBoarding": false}, {"Interest": "forests", "SimilarInterests": "forest,леса", "onBoarding": false}, {"Interest": "chocolate", "SimilarInterests": "chocolat,chocolade,čokoláda", "onBoarding": false}, {"Interest": "3dprinting", "SimilarInterests": "3ddruck,3dprint,impresion3d,3dtisk,impression3d", "onBoarding": false}, {"Interest": "sailing", "SimilarInterests": "segeln,sailinglife", "onBoarding": false}, {"Interest": "stoicism", "SimilarInterests": "estoicis<PERSON>,stoic", "onBoarding": false}, {"Interest": "sky", "SimilarInterests": "cielo,ceu,небо,gökyüzü", "onBoarding": false}, {"Interest": "creativity", "SimilarInterests": "творчесто,cretividad", "onBoarding": false}, {"Interest": "sexologia", "SimilarInterests": "sexology,сексологія,seksuologia,sessuologia", "onBoarding": false}, {"Interest": "personality", "SimilarInterests": "personalidad,personalidades,personalità,kişilik", "onBoarding": false}, {"Interest": "videography", "SimilarInterests": "видеосъёмка", "onBoarding": false}, {"Interest": "christmas", "SimilarInterests": "navidad,natale,kerst,v<PERSON><PERSON><PERSON>,van<PERSON><PERSON>,ka<PERSON><PERSON><PERSON><PERSON>,xmasday,xmass", "onBoarding": false}, {"Interest": "birthday", "SimilarInterests": "cumplea<PERSON>,<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,birthdays,compleanno,g<PERSON><PERSON><PERSON><PERSON>,деньнародження", "onBoarding": false}, {"Interest": "reptiles", "SimilarInterests": "reptilien", "onBoarding": false}, {"Interest": "نصائح_الحياة", "SimilarInterests": "lifetip", "onBoarding": false}, {"Interest": "dio", "SimilarInterests": "god,deus,dios,allah", "onBoarding": true}, {"Interest": "artist", "SimilarInterests": "artista", "onBoarding": false}, {"Interest": "magic", "SimilarInterests": "magia,magie,zauberei,magictricks,magick", "onBoarding": false}, {"Interest": "cartoon", "SimilarInterests": "caricatura,dessinanimé,çizgifilm,karikatür,karikatur", "onBoarding": false}, {"Interest": "college", "SimilarInterests": "faculdade,faculdades", "onBoarding": false}, {"Interest": "wildlife", "SimilarInterests": "fauna,живаяприрода", "onBoarding": false}, {"Interest": "pagan", "SimilarInterests": "paganism", "onBoarding": false}, {"Interest": "gamedev", "SimilarInterests": "gamedeveloper", "onBoarding": false}, {"Interest": "submissive", "SimilarInterests": "submissiveman", "onBoarding": false}, {"Interest": "horses", "SimilarInterests": "horse,caballos,pferd,cavalo,caballo", "onBoarding": false}, {"Interest": "chemistry", "SimilarInterests": "quimica", "onBoarding": false}, {"Interest": "neurodiversity", "SimilarInterests": "neurodivergencia", "onBoarding": false}, {"Interest": "theoffice", "SimilarInterests": "theofficce", "onBoarding": false}, {"Interest": "graphicdesign", "SimilarInterests": "<PERSON>des<PERSON><PERSON>,dise<PERSON><PERSON><PERSON><PERSON>,diseñogr<PERSON><PERSON><PERSON>", "onBoarding": false}, {"Interest": "countrymusic", "SimilarInterests": "<PERSON><PERSON><PERSON><PERSON>", "onBoarding": false}, {"Interest": "whiskey", "SimilarInterests": "whisky,viski", "onBoarding": false}, {"Interest": "horsebackriding", "SimilarInterests": "j<PERSON><PERSON><PERSON><PERSON>,horseriding", "onBoarding": false}, {"Interest": "self", "SimilarInterests": "b<PERSON><PERSON><PERSON><PERSON>", "onBoarding": false}, {"Interest": "advice", "SimilarInterests": "conselhos,consiglio,advise,advices", "onBoarding": false}, {"Interest": "sexhumor", "SimilarInterests": "sexualhumor", "onBoarding": false}, {"Interest": "sewing", "SimilarInterests": "costurar,шитье,sewingproject", "onBoarding": false}, {"Interest": "tvseries", "SimilarInterests": "сериал", "onBoarding": false}, {"Interest": "comicbooks", "SimilarInterests": "comicbook,historietas", "onBoarding": false}, {"Interest": "entrepreneur", "SimilarInterests": "entrepreneurship,emprendimiento,empreendedorismo", "onBoarding": false}, {"Interest": "empathy", "SimilarInterests": "empatia", "onBoarding": false}, {"Interest": "kiss", "SimilarInterests": "kissing,beijar", "onBoarding": false}, {"Interest": "usa", "SimilarInterests": "us", "onBoarding": false}, {"Interest": "bonfires", "SimilarInterests": "bonfire", "onBoarding": false}, {"Interest": "myself", "SimilarInterests": "kendim", "onBoarding": false}, {"Interest": "bible", "SimilarInterests": "biblia,bíblia", "onBoarding": false}, {"Interest": "streaming", "SimilarInterests": "stream", "onBoarding": false}, {"Interest": "happy", "SimilarInterests": "bahagia,heureux,felice,felizdia,happydays,happymoments,momentosfelizes", "onBoarding": false}, {"Interest": "mechanic", "SimilarInterests": "mecanico", "onBoarding": false}, {"Interest": "drinks", "SimilarInterests": "bebidas,minuman,beverage,напитки,içecek", "onBoarding": false}, {"Interest": "alternativerock", "SimilarInterests": "rockalternativo", "onBoarding": false}, {"Interest": "icecream", "SimilarInterests": "<PERSON><PERSON><PERSON>", "onBoarding": false}, {"Interest": "streamer", "SimilarInterests": "streamers", "onBoarding": false}, {"Interest": "deeptalk", "SimilarInterests": "char<PERSON><PERSON><PERSON><PERSON>", "onBoarding": false}, {"Interest": "thunderstorms", "SimilarInterests": "thunderstorm", "onBoarding": false}, {"Interest": "zombies", "SimilarInterests": "zombie", "onBoarding": false}, {"Interest": "healing", "SimilarInterests": "heilung,heal,healed", "onBoarding": false}, {"Interest": "soul", "SimilarInterests": "anima", "onBoarding": false}, {"Interest": "weather", "SimilarInterests": "meteo,wetter,wheather,pogoda,po<PERSON><PERSON><PERSON>,pocasi,天気", "onBoarding": false}, {"Interest": "hair", "SimilarInterests": "cabelo", "onBoarding": false}, {"Interest": "stars", "SimilarInterests": "estrellas,star", "onBoarding": false}, {"Interest": "wellness", "SimilarInterests": "benessere", "onBoarding": false}, {"Interest": "curiosity", "SimilarInterests": "curiosidad,curiosidade,curiosità,curiosita,c<PERSON>aw<PERSON>,curiozitate", "onBoarding": false}, {"Interest": "occult", "SimilarInterests": "occultism,ocultismo,оккультизм", "onBoarding": false}, {"Interest": "<PERSON><PERSON><PERSON>", "SimilarInterests": "humou<PERSON>ir", "onBoarding": false}, {"Interest": "petlover", "SimilarInterests": "doglover,animaisdeestimação,doglove", "onBoarding": false}, {"Interest": "therapy", "SimilarInterests": "therapist,terapeuta", "onBoarding": false}, {"Interest": "summer", "SimilarInterests": "лето,summertime,verao,καλοκαίρι", "onBoarding": false}, {"Interest": "communication", "SimilarInterests": "comunicacion,communicating,comunicação,kommuikation,iletişim", "onBoarding": false}, {"Interest": "psychedelics", "SimilarInterests": "psychedelic", "onBoarding": false}, {"Interest": "witch", "SimilarInterests": "ведьма,sorciere,witches,bruxas", "onBoarding": false}, {"Interest": "vtuber", "SimilarInterests": "vtubers", "onBoarding": false}, {"Interest": "truth", "SimilarInterests": "verità,verita,verite,verdades,truths", "onBoarding": false}, {"Interest": "sociology", "SimilarInterests": "sociologia,sociologie", "onBoarding": false}, {"Interest": "law", "SimilarInterests": "direito", "onBoarding": false}, {"Interest": "pool", "SimilarInterests": "piscina,havuz", "onBoarding": false}, {"Interest": "home", "SimilarInterests": "casa,дом,rumah,hogar,zuhause", "onBoarding": false}, {"Interest": "viking", "SimilarInterests": "vikings", "onBoarding": false}, {"Interest": "hola", "SimilarInterests": "hola️️", "onBoarding": false}, {"Interest": "mountainbiking", "SimilarInterests": "mountainbike,ciclismodemontaña", "onBoarding": false}, {"Interest": "dark", "SimilarInterests": "darkness", "onBoarding": false}, {"Interest": "audiobooks", "SimilarInterests": "audiobook", "onBoarding": false}, {"Interest": "hangout", "SimilarInterests": "hangingout,nongkrong", "onBoarding": false}, {"Interest": "musicproduction", "SimilarInterests": "produccionmusical", "onBoarding": false}, {"Interest": "bbq", "SimilarInterests": "barbecue,asado", "onBoarding": false}, {"Interest": "winter", "SimilarInterests": "invierno,inverno,zima,hiver", "onBoarding": false}, {"Interest": "study", "SimilarInterests": "estudo,studying,estudando,estudios,studies", "onBoarding": false}, {"Interest": "smoking", "SimilarInterests": "smoke,fumar,fumo", "onBoarding": false}, {"Interest": "goodmorning", "SimilarInterests": "bom<PERSON>,buen<PERSON><PERSON><PERSON>,buo<PERSON><PERSON><PERSON><PERSON><PERSON>,صبا<PERSON>و,do<PERSON><PERSON><PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,buo<PERSON>,goodmorning️,dias<PERSON><PERSON>s", "onBoarding": false}, {"Interest": "insomnia", "SimilarInterests": "insomnio,uykusuzluk,insônia,insomniac,insomnie", "onBoarding": false}, {"Interest": "deepthoughts", "SimilarInterests": "showerthought,deepthought", "onBoarding": false}, {"Interest": "animeart", "SimilarInterests": "desenhoanime", "onBoarding": false}, {"Interest": "bbw", "SimilarInterests": "bbws", "onBoarding": false}, {"Interest": "people", "SimilarInterests": "personas,ludzie,lid<PERSON>,o<PERSON><PERSON>", "onBoarding": false}, {"Interest": "freedom", "SimilarInterests": "libertad,libert<PERSON>,חו<PERSON><PERSON>", "onBoarding": false}, {"Interest": "carshows", "SimilarInterests": "carshow", "onBoarding": false}, {"Interest": "craftbeer", "SimilarInterests": "bi<PERSON><PERSON><PERSON><PERSON><PERSON>,cervezaartesanal,cervejaartesenal", "onBoarding": false}, {"Interest": "nudist", "SimilarInterests": "nudisme,nudists", "onBoarding": false}, {"Interest": "americanfootball", "SimilarInterests": "footballamericain", "onBoarding": false}, {"Interest": "restaurants", "SimilarInterests": "restaurant,рестораны", "onBoarding": false}, {"Interest": "alcohol", "SimilarInterests": "liquor,licores", "onBoarding": false}, {"Interest": "domingo", "SimilarInterests": "sunday,sonntag,воскресенье,mingg<PERSON><PERSON><PERSON>", "onBoarding": false}, {"Interest": "trucks", "SimilarInterests": "truck", "onBoarding": false}, {"Interest": "counterstrike", "SimilarInterests": "counterstrike2", "onBoarding": true}, {"Interest": "church", "SimilarInterests": "iglesia", "onBoarding": false}, {"Interest": "superheroes", "SimilarInterests": "superhero", "onBoarding": false}, {"Interest": "snakes", "SimilarInterests": "snake,schlangen", "onBoarding": false}, {"Interest": "paddleboarding", "SimilarInterests": "сапборд", "onBoarding": false}, {"Interest": "economics", "SimilarInterests": "economia,economy,ekonomi,economic", "onBoarding": false}, {"Interest": "overthinking", "SimilarInterests": "overthink", "onBoarding": false}, {"Interest": "new", "SimilarInterests": "nuevo", "onBoarding": false}, {"Interest": "boba", "SimilarInterests": "bobatea", "onBoarding": false}, {"Interest": "doom", "SimilarInterests": "doomer", "onBoarding": true}, {"Interest": "feelings", "SimilarInterests": "<PERSON><PERSON><PERSON><PERSON>,tìnhcảm", "onBoarding": false}, {"Interest": "naturewalks", "SimilarInterests": "natuurwandelingen,procházkypřírodě", "onBoarding": false}, {"Interest": "drugs", "SimilarInterests": "drogue", "onBoarding": false}, {"Interest": "sunrise", "SimilarInterests": "sunrises,son<PERSON><PERSON><PERSON><PERSON><PERSON>,aman<PERSON><PERSON>,amane<PERSON>,рассвет,dawn", "onBoarding": false}, {"Interest": "patience", "SimilarInterests": "paciencia", "onBoarding": false}, {"Interest": "wow", "SimilarInterests": "eita", "onBoarding": false}, {"Interest": "selfawareness", "SimilarInterests": "önismeret", "onBoarding": false}, {"Interest": "stockmarket", "SimilarInterests": "borsa", "onBoarding": false}, {"Interest": "billiards", "SimilarInterests": "billard,billiard", "onBoarding": false}, {"Interest": "socialmedia", "SimilarInterests": "sosy<PERSON><PERSON><PERSON>,соц<PERSON>е<PERSON>и,reseaux<PERSON><PERSON><PERSON>", "onBoarding": false}, {"Interest": "conventions", "SimilarInterests": "convention,convenciones", "onBoarding": false}, {"Interest": "vampires", "SimilarInterests": "vampire,vampiros", "onBoarding": false}, {"Interest": "comer", "SimilarInterests": "eating,manger,eat,makan", "onBoarding": false}, {"Interest": "picture", "SimilarInterests": "pictura,resim,gambar", "onBoarding": false}, {"Interest": "farming", "SimilarInterests": "farm,fazenda", "onBoarding": false}, {"Interest": "anthropology", "SimilarInterests": "antropología", "onBoarding": false}, {"Interest": "videoediting", "SimilarInterests": "ediçãodevideo", "onBoarding": false}, {"Interest": "paisaje", "SimilarInterests": "paisagem,landscape,paysage,paisage,táj", "onBoarding": false}, {"Interest": "trilha", "SimilarInterests": "trilhas", "onBoarding": false}, {"Interest": "cochesymotos", "SimilarInterests": "carsandbikes", "onBoarding": false}, {"Interest": "rollerskating", "SimilarInterests": "patinar,rollerskate,patina<PERSON><PERSON><PERSON><PERSON>", "onBoarding": false}, {"Interest": "day", "SimilarInterests": "daybyday,diadia,journ<PERSON>,giornata,daytoday,hari", "onBoarding": false}, {"Interest": "collecting", "SimilarInterests": "coleccionar,collezionismo", "onBoarding": false}, {"Interest": "stocks", "SimilarInterests": "stock", "onBoarding": false}, {"Interest": "friendswithbenefit", "SimilarInterests": "amigosconderechos,amistadconbeneficios,amizadecombenefícios,amigaconderecho", "onBoarding": false}, {"Interest": "mushrooms", "SimilarInterests": "mushroom,grzyby,champignon", "onBoarding": false}, {"Interest": "photographer", "SimilarInterests": "fotograf,фотограф,photographe", "onBoarding": false}, {"Interest": "vibes", "SimilarInterests": "vibe", "onBoarding": false}, {"Interest": "italia", "SimilarInterests": "italy", "onBoarding": false}, {"Interest": "programador", "SimilarInterests": "programmer", "onBoarding": false}, {"Interest": "job", "SimilarInterests": "робота,chamba,côngviệc", "onBoarding": false}, {"Interest": "threesome", "SimilarInterests": "ffm", "onBoarding": false}, {"Interest": "school", "SimilarInterests": "escola,школа,okul,sekolah", "onBoarding": false}, {"Interest": "autumn", "SimilarInterests": "<PERSON><PERSON><PERSON>,jesi<PERSON><PERSON>,automne,podzim,осінь", "onBoarding": false}, {"Interest": "teaching", "SimilarInterests": "docencia", "onBoarding": false}, {"Interest": "training", "SimilarInterests": "entrenamiento,entrainement,treinamento", "onBoarding": false}, {"Interest": "feeling", "SimilarInterests": "feel,feels,sentir", "onBoarding": false}, {"Interest": "software", "SimilarInterests": "<PERSON><PERSON><PERSON>", "onBoarding": false}, {"Interest": "happiness", "SimilarInterests": "felicidad,felicita", "onBoarding": false}, {"Interest": "sitcoms", "SimilarInterests": "sitcom", "onBoarding": false}, {"Interest": "sun", "SimilarInterests": "soleil,güne<PERSON>,сонце,slunce", "onBoarding": false}, {"Interest": "help", "SimilarInterests": "ayuda,ajuda,socorro", "onBoarding": false}, {"Interest": "riding", "SimilarInterests": "rides", "onBoarding": false}, {"Interest": "kids", "SimilarInterests": "children,enfants,d<PERSON><PERSON>,b<PERSON><PERSON>i", "onBoarding": false}, {"Interest": "goodvibes", "SimilarInterests": "buen<PERSON><PERSON>,curt<PERSON><PERSON>,buen<PERSON><PERSON>,bon<PERSON><PERSON><PERSON>,bom<PERSON><PERSON>", "onBoarding": false}, {"Interest": "goodnight", "SimilarInterests": "boanoitee,buenasnoches", "onBoarding": false}, {"Interest": "parenting", "SimilarInterests": "rodzicielstwo", "onBoarding": false}, {"Interest": "mychemicalromance", "SimilarInterests": "mychemicalromamce", "onBoarding": false}, {"Interest": "gothaesthetic", "SimilarInterests": "gothicaesthetic", "onBoarding": false}, {"Interest": "snow", "SimilarInterests": "neve,снег", "onBoarding": false}, {"Interest": "smile", "SimilarInterests": "sorrir,son<PERSON><PERSON><PERSON>,sourire,sonrie,sorria", "onBoarding": false}, {"Interest": "curious", "SimilarInterests": "curiosa", "onBoarding": false}, {"Interest": "geopolitics", "SimilarInterests": "geopolítica,geopolityka", "onBoarding": false}, {"Interest": "holiday", "SimilarInterests": "holidays,bayram,празник,ferie,swieta,feiertage,svátek", "onBoarding": false}, {"Interest": "illustration", "SimilarInterests": "ilustración,ilustracion", "onBoarding": false}, {"Interest": "rabbits", "SimilarInterests": "rabbit,kanin<PERSON>,кролики,bunny,conejo,conejito", "onBoarding": false}, {"Interest": "cdrama", "SimilarInterests": "cdramas", "onBoarding": false}, {"Interest": "theology", "SimilarInterests": "teologia", "onBoarding": false}, {"Interest": "marriage", "SimilarInterests": "evlilik,nikah", "onBoarding": false}, {"Interest": "adventuretime", "SimilarInterests": "horadeaventura", "onBoarding": false}, {"Interest": "plushies", "SimilarInterests": "plushie", "onBoarding": false}, {"Interest": "japanculture", "SimilarInterests": "japaneseculture", "onBoarding": false}, {"Interest": "eğlenmek", "SimilarInterests": "havefun", "onBoarding": false}, {"Interest": "geology", "SimilarInterests": "geologia", "onBoarding": false}, {"Interest": "frio", "SimilarInterests": "frío,dingin", "onBoarding": false}, {"Interest": "daydreaming", "SimilarInterests": "daydream,devan<PERSON>o", "onBoarding": false}, {"Interest": "racecars", "SimilarInterests": "racingcars,carrosesportivos,autosdeportivos", "onBoarding": false}, {"Interest": "pasta", "SimilarInterests": "nudeln", "onBoarding": false}, {"Interest": "<PERSON><PERSON><PERSON><PERSON>", "SimilarInterests": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "onBoarding": true}, {"Interest": "reality", "SimilarInterests": "realidade,gerçeklik,réalité,realtà,realität", "onBoarding": false}, {"Interest": "plant", "SimilarInterests": "pflanze", "onBoarding": false}, {"Interest": "trust", "SimilarInterests": "confianza,confiance", "onBoarding": false}, {"Interest": "mujeres", "SimilarInterests": "womens,wanita,жіноче", "onBoarding": false}, {"Interest": "personalgrowth", "SimilarInterests": "crescitapresonale", "onBoarding": false}, {"Interest": "mindset", "SimilarInterests": "etatdesprit", "onBoarding": false}, {"Interest": "student", "SimilarInterests": "estudante", "onBoarding": false}, {"Interest": "<PERSON><PERSON>ta", "SimilarInterests": "creepypastas", "onBoarding": false}, {"Interest": "newyear", "SimilarInterests": "newyearseve,newyearsday,yeniyıl,réveillon,anonov", "onBoarding": false}, {"Interest": "atheism", "SimilarInterests": "ateísmo", "onBoarding": false}, {"Interest": "flying", "SimilarInterests": "flight,fly", "onBoarding": false}, {"Interest": "trip", "SimilarInterests": "výlety,trips,výlet,поездка,vylet,путешествия,путишествия,viaggi,vylety", "onBoarding": false}, {"Interest": "equality", "SimilarInterests": "i<PERSON>lda<PERSON>", "onBoarding": false}, {"Interest": "water", "SimilarInterests": "agua", "onBoarding": false}, {"Interest": "black", "SimilarInterests": "siyah", "onBoarding": false}, {"Interest": "model", "SimilarInterests": "maquetas", "onBoarding": false}, {"Interest": "dragons", "SimilarInterests": "dragon", "onBoarding": false}, {"Interest": "girlfriend", "SimilarInterests": "namorada,noviasgo,novias", "onBoarding": false}, {"Interest": "writer", "SimilarInterests": "writers,penulis", "onBoarding": false}, {"Interest": "interiordesign", "SimilarInterests": "интерьер", "onBoarding": false}, {"Interest": "wicca", "SimilarInterests": "wiccan", "onBoarding": false}, {"Interest": "thewitcher", "SimilarInterests": "ведьмак3", "onBoarding": false}, {"Interest": "startup", "SimilarInterests": "startups", "onBoarding": false}, {"Interest": "boating", "SimilarInterests": "boats,boat", "onBoarding": false}, {"Interest": "facts", "SimilarInterests": "fakten", "onBoarding": false}, {"Interest": "healthandwellness", "SimilarInterests": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "onBoarding": false}, {"Interest": "conspiracies", "SimilarInterests": "conspiraciones", "onBoarding": false}, {"Interest": "cachorro", "SimilarInterests": "puppy,perrito,filhotes,puppies", "onBoarding": false}, {"Interest": "esoteric", "SimilarInterests": "esoterismo,esotericism,эзотерик", "onBoarding": false}, {"Interest": "survival", "SimilarInterests": "виживання", "onBoarding": false}, {"Interest": "escaperoom", "SimilarInterests": "escaperooms", "onBoarding": false}, {"Interest": "general", "SimilarInterests": "ทั่วไป,generale,allgemeine", "onBoarding": false}, {"Interest": "sériesefilmes", "SimilarInterests": "sériefilms", "onBoarding": false}, {"Interest": "hacking", "SimilarInterests": "hack", "onBoarding": false}, {"Interest": "doctor", "SimilarInterests": "medico,medecin,doctors", "onBoarding": false}, {"Interest": "arcadegames", "SimilarInterests": "arcade", "onBoarding": true}, {"Interest": "repas", "SimilarInterests": "meal", "onBoarding": false}, {"Interest": "trains", "SimilarInterests": "поезда", "onBoarding": false}, {"Interest": "nightlife", "SimilarInterests": "gecehayatı", "onBoarding": false}, {"Interest": "sketching", "SimilarInterests": "sketch,croquis", "onBoarding": false}, {"Interest": "vacation", "SimilarInterests": "ferias,vacance,vacanze,vacations,vakantie,vacacion,férias,wakacje,відпустка", "onBoarding": false}, {"Interest": "brasil", "SimilarInterests": "brazil", "onBoarding": false}, {"Interest": "caminarna<PERSON><PERSON><PERSON>", "SimilarInterests": "walkandnature", "onBoarding": false}, {"Interest": "arts", "SimilarInterests": "artes", "onBoarding": false}, {"Interest": "swiss", "SimilarInterests": "suisse,schweiz,switzerland", "onBoarding": false}, {"Interest": "mexico", "SimilarInterests": "méxico", "onBoarding": false}, {"Interest": "perfume", "SimilarInterests": "parfum,perfumes", "onBoarding": false}, {"Interest": "exploration", "SimilarInterests": "poznawanie", "onBoarding": false}, {"Interest": "naps", "SimilarInterests": "napping,nap", "onBoarding": false}, {"Interest": "españa", "SimilarInterests": "spain", "onBoarding": false}, {"Interest": "80smusic", "SimilarInterests": "musica80", "onBoarding": false}, {"Interest": "passionate", "SimilarInterests": "passionale", "onBoarding": false}, {"Interest": "clothes", "SimilarInterests": "ropa,clothing,oblečení", "onBoarding": false}, {"Interest": "filosofa", "SimilarInterests": "philosopher", "onBoarding": false}, {"Interest": "workaholic", "SimilarInterests": "iş<PERSON>lik,workaholics", "onBoarding": false}, {"Interest": "qualitytime", "SimilarInterests": "tiempodecalidad,tempodequalidade", "onBoarding": false}, {"Interest": "tired", "SimilarInterests": "cansado,lelah,müde", "onBoarding": false}, {"Interest": "ask", "SimilarInterests": "preguntar,vraag", "onBoarding": false}, {"Interest": "breakfast", "SimilarInterests": "colazione,desayunos,frühstück,snídaně,<PERSON><PERSON><PERSON><PERSON>", "onBoarding": false}, {"Interest": "rencontre", "SimilarInterests": "meeting,spotkania,rendezvous", "onBoarding": false}, {"Interest": "frogs", "SimilarInterests": "frog", "onBoarding": false}, {"Interest": "nails", "SimilarInterests": "unhas,nail", "onBoarding": false}, {"Interest": "gospel", "SimilarInterests": "evangel<PERSON>", "onBoarding": false}, {"Interest": "reflexion", "SimilarInterests": "reflection,reflexão,reflections,réflexion,prz<PERSON><PERSON><PERSON><PERSON><PERSON>,roz<PERSON><PERSON><PERSON>a", "onBoarding": false}, {"Interest": "france", "SimilarInterests": "frances", "onBoarding": false}, {"Interest": "opinion", "SimilarInterests": "opinions,opinar,opiniões,opinione,opina", "onBoarding": false}, {"Interest": "indoor", "SimilarInterests": "indoors", "onBoarding": false}, {"Interest": "positive", "SimilarInterests": "позитив,positively", "onBoarding": false}, {"Interest": "campfire", "SimilarInterests": "lager<PERSON><PERSON>", "onBoarding": false}, {"Interest": "lifestyle", "SimilarInterests": "stiledivita", "onBoarding": false}, {"Interest": "affectionate", "SimilarInterests": "affection", "onBoarding": false}, {"Interest": "nonmonogamy", "SimilarInterests": "nomonogamia", "onBoarding": false}, {"Interest": "rats", "SimilarInterests": "rat", "onBoarding": false}, {"Interest": "insects", "SimilarInterests": "insect,insecto,insetos", "onBoarding": false}, {"Interest": "monogamy", "SimilarInterests": "monogamia", "onBoarding": false}, {"Interest": "dessert", "SimilarInterests": "desserts,postre", "onBoarding": false}, {"Interest": "valentinesday", "SimilarInterests": "diado<PERSON><PERSON><PERSON><PERSON>,vale<PERSON><PERSON><PERSON>,wa<PERSON><PERSON><PERSON>,sev<PERSON><PERSON>lergünü", "onBoarding": false}, {"Interest": "app", "SimilarInterests": "apps,aplicativos,приложения", "onBoarding": false}, {"Interest": "healthcare", "SimilarInterests": "cuidadosalud", "onBoarding": false}, {"Interest": "karting", "SimilarInterests": "кар<PERSON><PERSON><PERSON><PERSON>", "onBoarding": false}, {"Interest": "sorties", "SimilarInterests": "salid<PERSON>,outing", "onBoarding": false}, {"Interest": "cosplayer", "SimilarInterests": "cosplayers", "onBoarding": false}, {"Interest": "ufo", "SimilarInterests": "ovni,ufologia", "onBoarding": false}, {"Interest": "friday", "SimilarInterests": "vendredi,venerdi,venerd<PERSON>", "onBoarding": false}, {"Interest": "modeling", "SimilarInterests": "modelarstwo,modelling,modelaje", "onBoarding": false}, {"Interest": "dirtbikes", "SimilarInterests": "dirtbike", "onBoarding": false}, {"Interest": "satanism", "SimilarInterests": "satanist,satanic,satanismo", "onBoarding": false}, {"Interest": "clouds", "SimilarInterests": "nubes,mraky,nuvole", "onBoarding": false}, {"Interest": "astrophotography", "SimilarInterests": "astrofotografía", "onBoarding": false}, {"Interest": "planes", "SimilarInterests": "airplanes,aircraft,airplane,plane,avião", "onBoarding": false}, {"Interest": "spiritualawakening", "SimilarInterests": "despertarespiritual", "onBoarding": false}, {"Interest": "contentcreation", "SimilarInterests": "contentcreating", "onBoarding": false}, {"Interest": "shisha", "SimilarInterests": "hookah,кальяны,narguile", "onBoarding": false}, {"Interest": "today", "SimilarInterests": "aujourdhui,сегодня", "onBoarding": false}, {"Interest": "drone", "SimilarInterests": "drones", "onBoarding": false}, {"Interest": "palestine", "SimilarInterests": "palestina", "onBoarding": false}, {"Interest": "spongebob", "SimilarInterests": "bobe<PERSON><PERSON><PERSON>,spongebobmemes", "onBoarding": false}, {"Interest": "nightdrives", "SimilarInterests": "nighdrives", "onBoarding": false}, {"Interest": "agriculture", "SimilarInterests": "rolnictwo,landwirtschaft,agricultura,agricoltura", "onBoarding": false}, {"Interest": "memories", "SimilarInterests": "ricordi,memorias,kenangan,memisek,memory,anı,memoria", "onBoarding": false}, {"Interest": "trees", "SimilarInterests": "tree,arboles,bäume,дерево", "onBoarding": false}, {"Interest": "turtles", "SimilarInterests": "s<PERSON><PERSON><PERSON><PERSON><PERSON>", "onBoarding": false}, {"Interest": "instrument", "SimilarInterests": "instruments", "onBoarding": false}, {"Interest": "naturalbeauty", "SimilarInterests": "bellezanatural", "onBoarding": false}, {"Interest": "wisdom", "SimilarInterests": "weisheit,mądrości", "onBoarding": false}, {"Interest": "texting", "SimilarInterests": "text,teks", "onBoarding": false}, {"Interest": "streetart", "SimilarInterests": "sztukauliczna", "onBoarding": false}, {"Interest": "metaphysics", "SimilarInterests": "metafisica", "onBoarding": false}, {"Interest": "nursing", "SimilarInterests": "nurse,en<PERSON><PERSON><PERSON>,en<PERSON><PERSON><PERSON>,enfer<PERSON><PERSON>", "onBoarding": false}, {"Interest": "clima", "SimilarInterests": "climate,climat", "onBoarding": false}, {"Interest": "you", "SimilarInterests": "ti", "onBoarding": false}, {"Interest": "houseplants", "SimilarInterests": "plantas<PERSON>a,plantasdeinterior,roślinywdomu", "onBoarding": false}, {"Interest": "embroidery", "SimilarInterests": "вышивание,bordados", "onBoarding": false}, {"Interest": "readbook", "SimilarInterests": "readingbook,lerlivros,kitapokuma", "onBoarding": false}, {"Interest": "questionaboutyou", "SimilarInterests": "questionsaboutyou", "onBoarding": false}, {"Interest": "realestate", "SimilarInterests": "bienesraices", "onBoarding": false}, {"Interest": "hobby", "SimilarInterests": "хоббі,hobbies,passatempi", "onBoarding": false}, {"Interest": "designer", "SimilarInterests": "модельер", "onBoarding": false}, {"Interest": "questionoftoday", "SimilarInterests": "questionsoftoday", "onBoarding": false}, {"Interest": "campo", "SimilarInterests": "countryside", "onBoarding": false}, {"Interest": "city", "SimilarInterests": "cities,cidade,ville,місто", "onBoarding": false}, {"Interest": "tattooartist", "SimilarInterests": "tatuador", "onBoarding": false}, {"Interest": "teacher", "SimilarInterests": "teachers,maestra", "onBoarding": false}, {"Interest": "classiccars", "SimilarInterests": "carrosclassicos,autosclasicos", "onBoarding": false}, {"Interest": "nothing", "SimilarInterests": "nada,nulla", "onBoarding": false}, {"Interest": "chubby", "SimilarInterests": "chubbygirls,chubbygirl", "onBoarding": false}, {"Interest": "creativewriting", "SimilarInterests": "escrituracreativa", "onBoarding": false}, {"Interest": "connection", "SimilarInterests": "connect,conexao", "onBoarding": false}, {"Interest": "mycology", "SimilarInterests": "micología", "onBoarding": false}, {"Interest": "equitation", "SimilarInterests": "equestrian", "onBoarding": false}, {"Interest": "inlineskating", "SimilarInterests": "patinsinline", "onBoarding": false}, {"Interest": "sensual", "SimilarInterests": "sensuality,sensualidade", "onBoarding": false}, {"Interest": "мотопрогулки", "SimilarInterests": "<PERSON><PERSON><PERSON><PERSON>", "onBoarding": false}, {"Interest": "coffeedates", "SimilarInterests": "coffeedate", "onBoarding": false}, {"Interest": "electricguitar", "SimilarInterests": "guitarraelectrica", "onBoarding": false}, {"Interest": "juegodemovil", "SimilarInterests": "mobilegames,videojuegoscelular,jogosdecelular", "onBoarding": true}, {"Interest": "amusementparks", "SimilarInterests": "freizeitpark,amusementpark", "onBoarding": false}, {"Interest": "openrelationship", "SimilarInterests": "relacionabierta,parejaabierta,relacionesabiertas", "onBoarding": false}, {"Interest": "dubai", "SimilarInterests": "uae", "onBoarding": false}, {"Interest": "twitchstreamer", "SimilarInterests": "twitchstreaming", "onBoarding": false}, {"Interest": "askmeanything", "SimilarInterests": "askanything", "onBoarding": false}, {"Interest": "hopelessromantic", "SimilarInterests": "hopelesslyromantic", "onBoarding": false}, {"Interest": "3dmodeling", "SimilarInterests": "modelado3d", "onBoarding": false}, {"Interest": "architect", "SimilarInterests": "arquitecto", "onBoarding": false}, {"Interest": "hamster", "SimilarInterests": "hamsters", "onBoarding": false}, {"Interest": "swords", "SimilarInterests": "sword", "onBoarding": false}, {"Interest": "compagnia", "SimilarInterests": "company", "onBoarding": false}, {"Interest": "bodypositive", "SimilarInterests": "bodypositivity", "onBoarding": false}, {"Interest": "vent", "SimilarInterests": "venting,desahogo", "onBoarding": false}, {"Interest": "growth", "SimilarInterests": "crecimiento", "onBoarding": false}, {"Interest": "stories", "SimilarInterests": "relatos,historias,cuentos", "onBoarding": false}, {"Interest": "energy", "SimilarInterests": "energia", "onBoarding": false}, {"Interest": "university", "SimilarInterests": "universidad,università,üniversite", "onBoarding": false}, {"Interest": "weightloss", "SimilarInterests": "emagrecimento", "onBoarding": false}, {"Interest": "band", "SimilarInterests": "banda", "onBoarding": false}, {"Interest": "houseofthedragon", "SimilarInterests": "houseofthedragons", "onBoarding": false}, {"Interest": "crazy", "SimilarInterests": "creazy", "onBoarding": false}, {"Interest": "emotional", "SimilarInterests": "emotion,emotions,emozioni,emocje,emocional,duygusal,emocionales,émotions", "onBoarding": false}, {"Interest": "asianfood", "SimilarInterests": "comidaasiatica", "onBoarding": false}, {"Interest": "beautiful", "SimilarInterests": "linda,красиво,canti<PERSON><PERSON>,hermosa,красивое,maganda", "onBoarding": false}, {"Interest": "construction", "SimilarInterests": "building,budownictwo,construcciones", "onBoarding": false}, {"Interest": "ceramics", "SimilarInterests": "ceramica", "onBoarding": false}, {"Interest": "discoteca", "SimilarInterests": "nightclub,nightclubs", "onBoarding": false}, {"Interest": "jewelry", "SimilarInterests": "jewelrymaking,jewelrycrafting,joyeria,šperky", "onBoarding": false}, {"Interest": "poland", "SimilarInterests": "polska", "onBoarding": false}, {"Interest": "català", "SimilarInterests": "catalan", "onBoarding": false}, {"Interest": "solitude", "SimilarInterests": "solidao,ekaant", "onBoarding": false}, {"Interest": "numerology", "SimilarInterests": "numerologia", "onBoarding": false}, {"Interest": "criminalminds", "SimilarInterests": "criminalmind", "onBoarding": false}, {"Interest": "scout", "SimilarInterests": "scouts", "onBoarding": false}, {"Interest": "tranquilidad", "SimilarInterests": "tranquilidade,tranquility", "onBoarding": false}, {"Interest": "blacksmithing", "SimilarInterests": "kovářství", "onBoarding": false}, {"Interest": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SimilarInterests": "jiujit<PERSON><PERSON><PERSON><PERSON><PERSON>", "onBoarding": false}, {"Interest": "<PERSON><PERSON><PERSON>", "SimilarInterests": "carnaval,carnivals", "onBoarding": true}, {"Interest": "bikeride", "SimilarInterests": "велопрогулянки,ridingbikes,ridebike", "onBoarding": false}, {"Interest": "sweets", "SimilarInterests": "dulces,сладости,dol<PERSON><PERSON>,tatl<PERSON><PERSON>,slad<PERSON><PERSON>,sweet,doce", "onBoarding": false}, {"Interest": "brat", "SimilarInterests": "brats", "onBoarding": false}, {"Interest": "rant", "SimilarInterests": "rants", "onBoarding": false}, {"Interest": "reader", "SimilarInterests": "readers", "onBoarding": false}, {"Interest": "crossdressing", "SimilarInterests": "crossdresing", "onBoarding": false}, {"Interest": "heartbreak", "SimilarInterests": "desamor,desamores,decep<PERSON><PERSON><PERSON><PERSON>", "onBoarding": false}, {"Interest": "breweries", "SimilarInterests": "c<PERSON><PERSON><PERSON>", "onBoarding": false}, {"Interest": "newbie", "SimilarInterests": "novato,novata", "onBoarding": false}, {"Interest": "freestyle", "SimilarInterests": "freestayle", "onBoarding": false}, {"Interest": "nailart", "SimilarInterests": "uñasdecoradas,manicura", "onBoarding": false}, {"Interest": "guineapig", "SimilarInterests": "<PERSON><PERSON><PERSON>weinchen,морскиесвинки", "onBoarding": false}, {"Interest": "kitesurfing", "SimilarInterests": "kitesurf", "onBoarding": false}, {"Interest": "existentialism", "SimilarInterests": "existencialismo,existential,esistenziale", "onBoarding": false}, {"Interest": "adulting", "SimilarInterests": "adulto", "onBoarding": false}, {"Interest": "cleaning", "SimilarInterests": "limpieza,temizlik", "onBoarding": false}, {"Interest": "ridingmotorcycles", "SimilarInterests": "motosikletsürmek", "onBoarding": false}, {"Interest": "haircut", "SimilarInterests": "corted<PERSON><PERSON><PERSON>,haircutting", "onBoarding": false}, {"Interest": "mom", "SimilarInterests": "mother,maman", "onBoarding": false}, {"Interest": "lyrics", "SimilarInterests": "letras,şarkısözleri", "onBoarding": false}, {"Interest": "fire", "SimilarInterests": "fuego,огонь", "onBoarding": false}, {"Interest": "pain", "SimilarInterests": "painful", "onBoarding": false}, {"Interest": "toys", "SimilarInterests": "игрушки", "onBoarding": false}, {"Interest": "career", "SimilarInterests": "карьера", "onBoarding": false}, {"Interest": "spiders", "SimilarInterests": "arañas", "onBoarding": false}, {"Interest": "coloring", "SimilarInterests": "colorier", "onBoarding": false}, {"Interest": "lifeexperience", "SimilarInterests": "experienciasdevida", "onBoarding": false}, {"Interest": "monday", "SimilarInterests": "lunes,montag,lunedi,luned<PERSON>", "onBoarding": false}, {"Interest": "muscle", "SimilarInterests": "muscu", "onBoarding": false}, {"Interest": "events", "SimilarInterests": "événement", "onBoarding": false}, {"Interest": "calm", "SimilarInterests": "calme", "onBoarding": false}, {"Interest": "russia", "SimilarInterests": "россия", "onBoarding": false}, {"Interest": "carpentry", "SimilarInterests": "carpin<PERSON>ia", "onBoarding": false}, {"Interest": "watches", "SimilarInterests": "orologi", "onBoarding": false}, {"Interest": "charlamos", "SimilarInterests": "conversaa,letstalks,parliamone,parliamo", "onBoarding": false}, {"Interest": "truelove", "SimilarInterests": "amordeverdade", "onBoarding": false}, {"Interest": "parks", "SimilarInterests": "парки", "onBoarding": false}, {"Interest": "tarotcardreadings", "SimilarInterests": "tarotcards", "onBoarding": false}, {"Interest": "europe", "SimilarInterests": "europa", "onBoarding": false}, {"Interest": "germany", "SimilarInterests": "deutschland,alemania", "onBoarding": false}, {"Interest": "waterfalls", "SimilarInterests": "waterfall", "onBoarding": false}, {"Interest": "parrots", "SimilarInterests": "parrot", "onBoarding": false}, {"Interest": "pies", "SimilarInterests": "pie,pi", "onBoarding": false}, {"Interest": "offgrid", "SimilarInterests": "offthegrid", "onBoarding": false}, {"Interest": "homeworkout", "SimilarInterests": "ejercicioencasa,t<PERSON><PERSON>emcasa,ejerciciosencasa", "onBoarding": false}, {"Interest": "humanity", "SimilarInterests": "<PERSON><PERSON><PERSON><PERSON><PERSON>,humanité", "onBoarding": false}, {"Interest": "onenightfriends", "SimilarInterests": "onenightfriend", "onBoarding": false}, {"Interest": "sportautomobile", "SimilarInterests": "sportcar", "onBoarding": false}, {"Interest": "meetingnewpeople", "SimilarInterests": "meetnewpeople,conoc<PERSON><PERSON><PERSON>s,conhecerpes<PERSON><PERSON>novas,meetingpeople,conhecernovaspessoas", "onBoarding": false}, {"Interest": "reels", "SimilarInterests": "reel", "onBoarding": false}, {"Interest": "90smusic", "SimilarInterests": "90music", "onBoarding": false}, {"Interest": "mixology", "SimilarInterests": "coctelería", "onBoarding": false}, {"Interest": "salirconami<PERSON>", "SimilarInterests": "uscireconglia<PERSON><PERSON>,встречисдрузьями", "onBoarding": false}, {"Interest": "spontaneous", "SimilarInterests": "spontan", "onBoarding": false}, {"Interest": "randomquestions", "SimilarInterests": "perguntaaleatoria", "onBoarding": false}, {"Interest": "naturephotography", "SimilarInterests": "fotografarnatureza,fotodenaturaleza,фотоприроды", "onBoarding": false}, {"Interest": "goals", "SimilarInterests": "goal,objetivo", "onBoarding": false}, {"Interest": "view", "SimilarInterests": "widoki", "onBoarding": false}, {"Interest": "dinner", "SimilarInterests": "cena<PERSON>,večeře", "onBoarding": false}, {"Interest": "worship", "SimilarInterests": "ibadah", "onBoarding": false}, {"Interest": "grilling", "SimilarInterests": "grillen", "onBoarding": false}, {"Interest": "chisme", "SimilarInterests": "gossip,fofocando", "onBoarding": false}, {"Interest": "turismo", "SimilarInterests": "tourism", "onBoarding": false}, {"Interest": "spring", "SimilarInterests": "primavera,весна", "onBoarding": false}, {"Interest": "rainydays", "SimilarInterests": "rainyday,díalluvioso", "onBoarding": false}, {"Interest": "ferrets", "SimilarInterests": "ferret", "onBoarding": false}, {"Interest": "healthy", "SimilarInterests": "gesund,saudavel", "onBoarding": false}, {"Interest": "artsandcrafts", "SimilarInterests": "artandcraft", "onBoarding": false}, {"Interest": "liverpoolfc", "SimilarInterests": "liverpool", "onBoarding": false}, {"Interest": "society", "SimilarInterests": "sociedad,gesellschaft,societá,sociétés,societies", "onBoarding": false}, {"Interest": "athlete", "SimilarInterests": "atleta", "onBoarding": false}, {"Interest": "contentcreator", "SimilarInterests": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "onBoarding": false}, {"Interest": "lake", "SimilarInterests": "lakes", "onBoarding": false}, {"Interest": "lizards", "SimilarInterests": "lizard", "onBoarding": false}, {"Interest": "bed", "SimilarInterests": "cama", "onBoarding": false}, {"Interest": "twodimensions", "SimilarInterests": "dosdimensiones", "onBoarding": false}, {"Interest": "tattogirl", "SimilarInterests": "tattoedgirl", "onBoarding": false}, {"Interest": "cdmx", "SimilarInterests": "mexicocity,ciudaddemexico", "onBoarding": false}, {"Interest": "sick", "SimilarInterests": "enferma,malade", "onBoarding": false}, {"Interest": "eyes", "SimilarInterests": "ojos", "onBoarding": false}, {"Interest": "entertainment", "SimilarInterests": "entretenimento,hiburan,развлечение,entretenimiento,rozrywka,divertissements,zábava", "onBoarding": false}, {"Interest": "happybirthday", "SimilarInterests": "cumple<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,felizcumple<PERSON><PERSON><PERSON><PERSON>", "onBoarding": false}, {"Interest": "latenight", "SimilarInterests": "latenightchat,latenightchats", "onBoarding": false}, {"Interest": "conspiracy", "SimilarInterests": "conspiracion", "onBoarding": false}, {"Interest": "coffeelover", "SimilarInterests": "кофемания", "onBoarding": false}, {"Interest": "christiandating", "SimilarInterests": "namorocrist<PERSON>", "onBoarding": false}, {"Interest": "psychoanalysis", "SimilarInterests": "psicanalise,psychanalyse,psicoanálisis", "onBoarding": false}, {"Interest": "merrychristmas", "SimilarInterests": "feliznavidad", "onBoarding": false}, {"Interest": "nightout", "SimilarInterests": "salidasnocturnas,nightouts", "onBoarding": false}, {"Interest": "austria", "SimilarInterests": "österreich", "onBoarding": false}, {"Interest": "keyboard", "SimilarInterests": "keyboards", "onBoarding": false}, {"Interest": "saturday", "SimilarInterests": "sábado,sabados,samstag,sábadou,sabadito,sabadou", "onBoarding": false}, {"Interest": "watercolor", "SimilarInterests": "a<PERSON><PERSON><PERSON>", "onBoarding": false}, {"Interest": "рукоделия", "SimilarInterests": "handcraft,artesanias,handcrafts,artesanía,artesanato,handmade,rękodzieło,хендмейд", "onBoarding": false}, {"Interest": "lovelife", "SimilarInterests": "жизнелюбие", "onBoarding": false}, {"Interest": "fashiondesign", "SimilarInterests": "couture,fashiondesigner", "onBoarding": false}, {"Interest": "funfacts", "SimilarInterests": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "onBoarding": false}, {"Interest": "sculpture", "SimilarInterests": "escultura", "onBoarding": false}, {"Interest": "married", "SimilarInterests": "<PERSON><PERSON><PERSON>", "onBoarding": false}, {"Interest": "diet", "SimilarInterests": "ダイエット", "onBoarding": false}, {"Interest": "extremesports", "SimilarInterests": "deport<PERSON><PERSON><PERSON><PERSON>,экстримспорт", "onBoarding": false}, {"Interest": "relazionisentimentali", "SimilarInterests": "relacionamorosa", "onBoarding": false}, {"Interest": "duda", "SimilarInterests": "<PERSON><PERSON>", "onBoarding": false}, {"Interest": "inteligenciartificial", "SimilarInterests": "<PERSON><PERSON><PERSON><PERSON><PERSON>,искуственныйинтелект", "onBoarding": false}, {"Interest": "teenagers", "SimilarInterests": "adolescentes", "onBoarding": false}, {"Interest": "fandom", "SimilarInterests": "fandoms", "onBoarding": false}, {"Interest": "karma", "SimilarInterests": "карма", "onBoarding": false}, {"Interest": "ancienthistory", "SimilarInterests": "historiaantigua,históriaantiga", "onBoarding": false}, {"Interest": "can be merged with #zodiac row 379", "SimilarInterests": "hor<PERSON><PERSON><PERSON>,гороскопы,horoscopes,гороскоп", "onBoarding": false}, {"Interest": "journalism", "SimilarInterests": "<PERSON><PERSON><PERSON><PERSON>", "onBoarding": false}, {"Interest": "bestfriend", "SimilarInterests": "amica", "onBoarding": false}, {"Interest": "citasamigos", "SimilarInterests": "frienddate,frienddates", "onBoarding": false}, {"Interest": "candles", "SimilarInterests": "candle,candlelight", "onBoarding": false}, {"Interest": "match", "SimilarInterests": "matches", "onBoarding": false}, {"Interest": "happynewyear", "SimilarInterests": "buonanno", "onBoarding": false}, {"Interest": "entomology", "SimilarInterests": "entomologia", "onBoarding": false}, {"Interest": "cookies", "SimilarInterests": "cookie,biscoito,galletas", "onBoarding": false}, {"Interest": "introspection", "SimilarInterests": "introspección", "onBoarding": false}, {"Interest": "prayer", "SimilarInterests": "prayers,dua", "onBoarding": false}, {"Interest": "awareness", "SimilarInterests": "consciencia,ś<PERSON><PERSON>mość", "onBoarding": false}, {"Interest": "adultchat", "SimilarInterests": "<PERSON><PERSON><PERSON><PERSON>", "onBoarding": false}, {"Interest": "turkey", "SimilarInterests": "türkiye", "onBoarding": false}, {"Interest": "doblajes", "SimilarInterests": "doppiaggio,dublando", "onBoarding": false}, {"Interest": "accounting", "SimilarInterests": "contabilidad", "onBoarding": false}, {"Interest": "ta<PERSON><PERSON>me", "SimilarInterests": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "onBoarding": false}, {"Interest": "burger", "SimilarInterests": "hamburguesa", "onBoarding": false}, {"Interest": "signos", "SimilarInterests": "zei<PERSON>,signs", "onBoarding": false}, {"Interest": "networking", "SimilarInterests": "network", "onBoarding": false}, {"Interest": "riddles", "SimilarInterests": "devinettes", "onBoarding": true}, {"Interest": "inspiration", "SimilarInterests": "inspiracion,натхнення", "onBoarding": false}, {"Interest": "collectables", "SimilarInterests": "collectibles,coleccionables", "onBoarding": false}, {"Interest": "nightwish", "SimilarInterests": "<PERSON><PERSON><PERSON><PERSON>", "onBoarding": false}, {"Interest": "modelkits", "SimilarInterests": "modelskit", "onBoarding": false}, {"Interest": "abandonedplaces", "SimilarInterests": "lugaresabandonados", "onBoarding": false}, {"Interest": "boyfriend", "SimilarInterests": "novio", "onBoarding": false}, {"Interest": "anatomy", "SimilarInterests": "anatomia", "onBoarding": false}, {"Interest": "twilight", "SimilarInterests": "senja,crep<PERSON><PERSON>lo,anochecer", "onBoarding": false}, {"Interest": "singlemom", "SimilarInterests": "singlemomsthought", "onBoarding": false}, {"Interest": "other", "SimilarInterests": "lain", "onBoarding": false}, {"Interest": "spiritualgrowth", "SimilarInterests": "crecimientoespiritual", "onBoarding": false}, {"Interest": "singleparent", "SimilarInterests": "singleparenting", "onBoarding": false}, {"Interest": "blue", "SimilarInterests": "azul", "onBoarding": false}, {"Interest": "selfreflection", "SimilarInterests": "autoanálise", "onBoarding": false}, {"Interest": "gambling", "SimilarInterests": "gamble", "onBoarding": false}, {"Interest": "trabajod<PERSON>", "SimilarInterests": "hardwork,trab<PERSON><PERSON><PERSON><PERSON>,k<PERSON><PERSON><PERSON><PERSON>,trabaja<PERSON><PERSON>,workhard,trabajanding", "onBoarding": false}, {"Interest": "confused", "SimilarInterests": "bingung,confundido,confusing,confuse", "onBoarding": false}, {"Interest": "selfhelp", "SimilarInterests": "autoayuda", "onBoarding": false}, {"Interest": "evening", "SimilarInterests": "sera,soir,ve<PERSON><PERSON>,a<PERSON><PERSON><PERSON>,soir<PERSON>,soiree", "onBoarding": false}, {"Interest": "message", "SimilarInterests": "recado", "onBoarding": false}, {"Interest": "tarantulas", "SimilarInterests": "vogelspinne", "onBoarding": false}, {"Interest": "blogging", "SimilarInterests": "blog,блогер,blogs,bloguero,blogue", "onBoarding": false}, {"Interest": "beachlife", "SimilarInterests": "pantaii", "onBoarding": false}, {"Interest": "preguntaexistencial", "SimilarInterests": "domandesistenzia<PERSON>", "onBoarding": false}, {"Interest": "capybara", "SimilarInterests": "<PERSON><PERSON><PERSON>", "onBoarding": false}, {"Interest": "wellbeing", "SimilarInterests": "bemestar", "onBoarding": false}, {"Interest": "nft", "SimilarInterests": "nfts", "onBoarding": false}, {"Interest": "mature", "SimilarInterests": "<PERSON>ura,matured", "onBoarding": false}, {"Interest": "moment", "SimilarInterests": "moments", "onBoarding": false}, {"Interest": "animallover", "SimilarInterests": "animalslover,hayvansever,tierlieb", "onBoarding": false}, {"Interest": "интимное", "SimilarInterests": "intimate", "onBoarding": false}, {"Interest": "pleasure", "SimilarInterests": "placer", "onBoarding": false}, {"Interest": "asperger", "SimilarInterests": "aspergers", "onBoarding": false}, {"Interest": "pirates", "SimilarInterests": "pirati", "onBoarding": false}, {"Interest": "information", "SimilarInterests": "información,informasi", "onBoarding": false}, {"Interest": "lost", "SimilarInterests": "perdida", "onBoarding": false}, {"Interest": "spaziereren", "SimilarInterests": "<PERSON><PERSON><PERSON><PERSON>", "onBoarding": false}, {"Interest": "talkaboutlife", "SimilarInterests": "hablardelavida,talklife", "onBoarding": false}, {"Interest": "vietnam", "SimilarInterests": "viet", "onBoarding": false}, {"Interest": "weekendvibes", "SimilarInterests": "finaisdesemana,saturdayvibes", "onBoarding": false}, {"Interest": "saludemocional", "SimilarInterests": "saúdeemociomal", "onBoarding": false}, {"Interest": "stress", "SimilarInterests": "stressed,estrés", "onBoarding": false}, {"Interest": "3ddesign", "SimilarInterests": "diseño3d", "onBoarding": false}, {"Interest": "manifesting", "SimilarInterests": "manifest", "onBoarding": false}, {"Interest": "walkingatnight", "SimilarInterests": "walkatnight", "onBoarding": false}, {"Interest": "travelaroundtheworld", "SimilarInterests": "viajesporelmundo", "onBoarding": false}, {"Interest": "nightsky", "SimilarInterests": "nachthimmel", "onBoarding": false}, {"Interest": "spiritualdevelopment", "SimilarInterests": "духовноеразвитие", "onBoarding": false}, {"Interest": "womanbeauty", "SimilarInterests": "belezafeminina", "onBoarding": false}, {"Interest": "touring", "SimilarInterests": "tour", "onBoarding": false}, {"Interest": "heart", "SimilarInterests": "hati,coeur,cuore", "onBoarding": false}, {"Interest": "kittens", "SimilarInterests": "g<PERSON><PERSON><PERSON>,cicák,kotki", "onBoarding": false}, {"Interest": "psychedelicrock", "SimilarInterests": "rockpsicodelico", "onBoarding": false}, {"Interest": "fragrance", "SimilarInterests": "fragrances,profumi", "onBoarding": false}, {"Interest": "confession", "SimilarInterests": "confesión,confiss<PERSON>,přiznání,curhat", "onBoarding": false}, {"Interest": "wedding", "SimilarInterests": "casamento,weddings,düğün", "onBoarding": false}, {"Interest": "poll", "SimilarInterests": "encuesta,sondage", "onBoarding": false}, {"Interest": "me3", "SimilarInterests": "saya,e<PERSON><PERSON><PERSON>,йа", "onBoarding": false}, {"Interest": "grateful", "SimilarInterests": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "onBoarding": false}, {"Interest": "dentistry", "SimilarInterests": "odontologia,dentist,dental", "onBoarding": false}, {"Interest": "longdistancerelations", "SimilarInterests": "relacionadistancia", "onBoarding": false}, {"Interest": "fight", "SimilarInterests": "fighting", "onBoarding": false}, {"Interest": "filipina", "SimilarInterests": "pinay", "onBoarding": false}, {"Interest": "truckdriver", "SimilarInterests": "lkw<PERSON><PERSON>er,truckdrivers", "onBoarding": false}, {"Interest": "gifts", "SimilarInterests": "regalo,hediye,подарунки", "onBoarding": false}, {"Interest": "freelance", "SimilarInterests": "freelancer", "onBoarding": false}, {"Interest": "catsanddogs", "SimilarInterests": "cats<PERSON><PERSON>,kedilerveköpekler", "onBoarding": false}, {"Interest": "rainbow", "SimilarInterests": "<PERSON><PERSON><PERSON>", "onBoarding": false}, {"Interest": "interesting", "SimilarInterests": "интересно,интересное,цікаве,interese,面白い", "onBoarding": false}, {"Interest": "knives", "SimilarInterests": "knife", "onBoarding": false}, {"Interest": "sexyphotos", "SimilarInterests": "fotossexy", "onBoarding": false}, {"Interest": "writingpoetry", "SimilarInterests": "escribirpoesía,escribir<PERSON><PERSON>s,писатьстихи,poetrywriting,стихосложение,escritapoetia", "onBoarding": false}, {"Interest": "words", "SimilarInterests": "katakata,palabras,keli<PERSON>er", "onBoarding": false}, {"Interest": "camera", "SimilarInterests": "camara", "onBoarding": false}, {"Interest": "parlare", "SimilarInterests": "speak", "onBoarding": false}, {"Interest": "krafttraining", "SimilarInterests": "strengthtraining", "onBoarding": false}, {"Interest": "tristerealidad", "SimilarInterests": "tristerealtà", "onBoarding": false}, {"Interest": "interracial", "SimilarInterests": "interraciallovematter", "onBoarding": false}, {"Interest": "veteran", "SimilarInterests": "veterans", "onBoarding": false}, {"Interest": "cowboy", "SimilarInterests": "vaquero", "onBoarding": false}, {"Interest": "red", "SimilarInterests": "rojo", "onBoarding": false}, {"Interest": "vidaadulta", "SimilarInterests": "adultlife", "onBoarding": false}, {"Interest": "analogphotography", "SimilarInterests": "analogovafotografie", "onBoarding": false}, {"Interest": "gratitude", "SimilarInterests": "<PERSON>rat<PERSON><PERSON>", "onBoarding": false}, {"Interest": "rest", "SimilarInterests": "is<PERSON><PERSON><PERSON>", "onBoarding": false}, {"Interest": "content", "SimilarInterests": "contenidos", "onBoarding": false}, {"Interest": "records", "SimilarInterests": "record", "onBoarding": false}, {"Interest": "collage", "SimilarInterests": "collageart", "onBoarding": false}, {"Interest": "breakup", "SimilarInterests": "breakups,rupture", "onBoarding": false}, {"Interest": "milano", "SimilarInterests": "milan", "onBoarding": false}, {"Interest": "<PERSON><PERSON><PERSON><PERSON>", "SimilarInterests": "paesaggi,paisagens,landscapes,paysages", "onBoarding": false}, {"Interest": "fasting", "SimilarInterests": "puasa", "onBoarding": false}, {"Interest": "motivate", "SimilarInterests": "motivateyourself", "onBoarding": false}, {"Interest": "comidacasera", "SimilarInterests": "cuisinemaison", "onBoarding": false}, {"Interest": "lunch", "SimilarInterests": "almoço,almuerzo,almoco,pranzo", "onBoarding": false}, {"Interest": "belgium", "SimilarInterests": "belgique", "onBoarding": false}, {"Interest": "socializing", "SimilarInterests": "socializar,socialize,sociabilizar,socializare,socializzare", "onBoarding": false}, {"Interest": "affirmations", "SimilarInterests": "affirmation,afirmaciones,afirmasi", "onBoarding": false}, {"Interest": "typeonegative", "SimilarInterests": "type0negative", "onBoarding": false}, {"Interest": "actionfigures", "SimilarInterests": "actionfigure", "onBoarding": false}, {"Interest": "filosofando", "SimilarInterests": "philosophieren,philosophies", "onBoarding": false}, {"Interest": "pretty", "SimilarInterests": "bonita", "onBoarding": false}, {"Interest": "bikers", "SimilarInterests": "moteros", "onBoarding": false}, {"Interest": "juggling", "SimilarInterests": "malabarismo", "onBoarding": false}, {"Interest": "chaos", "SimilarInterests": "chaotic", "onBoarding": false}, {"Interest": "human", "SimilarInterests": "humain,člověk", "onBoarding": false}, {"Interest": "юмористы", "SimilarInterests": "comedians,comedian,standupcomedian", "onBoarding": false}, {"Interest": "photoart", "SimilarInterests": "fotoart", "onBoarding": false}, {"Interest": "greetings", "SimilarInterests": "saludos,greeting,挨拶", "onBoarding": false}, {"Interest": "attraction", "SimilarInterests": "влечение", "onBoarding": false}, {"Interest": "countrylife", "SimilarInterests": "сельскаяжизнь", "onBoarding": false}, {"Interest": "fireworks", "SimilarInterests": "feuerwerk", "onBoarding": false}, {"Interest": "vlogging", "SimilarInterests": "vlog,vlogger", "onBoarding": false}, {"Interest": "cruise", "SimilarInterests": "kreuzfahrt", "onBoarding": false}, {"Interest": "<PERSON><PERSON><PERSON>", "SimilarInterests": "cabelocacheado", "onBoarding": false}, {"Interest": "brokenheart", "SimilarInterests": "corazonroto", "onBoarding": false}, {"Interest": "pickuplines", "SimilarInterests": "pickupline,piroposparahombres", "onBoarding": false}, {"Interest": "creation", "SimilarInterests": "creación,creacion", "onBoarding": false}, {"Interest": "storms", "SimilarInterests": "storm,tormentas", "onBoarding": false}, {"Interest": "theories", "SimilarInterests": "theory,teorias,teoria", "onBoarding": false}, {"Interest": "scrapbooking", "SimilarInterests": "scrapbook", "onBoarding": false}, {"Interest": "<PERSON><PERSON><PERSON>", "SimilarInterests": "nj", "onBoarding": false}, {"Interest": "saopaulo", "SimilarInterests": "<PERSON><PERSON><PERSON><PERSON>", "onBoarding": false}, {"Interest": "fisioterapia", "SimilarInterests": "physiotherapy", "onBoarding": false}, {"Interest": "enlightenment", "SimilarInterests": "просветление", "onBoarding": false}, {"Interest": "aboutme", "SimilarInterests": "sobremin,sobremí", "onBoarding": false}, {"Interest": "atividadesfísicas", "SimilarInterests": "physicalactivities", "onBoarding": false}, {"Interest": "simple", "SimilarInterests": "prosto", "onBoarding": false}, {"Interest": "citasapp", "SimilarInterests": "dateapp", "onBoarding": false}, {"Interest": "quran", "SimilarInterests": "القران", "onBoarding": false}, {"Interest": "test", "SimilarInterests": "testing,provas", "onBoarding": false}, {"Interest": "everything", "SimilarInterests": "tudo", "onBoarding": false}, {"Interest": "lucifer", "SimilarInterests": "luciferian", "onBoarding": false}, {"Interest": "iran", "SimilarInterests": "ایران", "onBoarding": false}, {"Interest": "comicartist", "SimilarInterests": "comicart", "onBoarding": false}, {"Interest": "redhead", "SimilarInterests": "ruiva", "onBoarding": false}, {"Interest": "glasses", "SimilarInterests": "gafas,ó<PERSON>los", "onBoarding": false}, {"Interest": "cigar", "SimilarInterests": "cigars,cigarro", "onBoarding": false}, {"Interest": "butterflies", "SimilarInterests": "butterfly,mariposa,papillon", "onBoarding": false}, {"Interest": "artdrawings", "SimilarInterests": "di<PERSON><PERSON><PERSON><PERSON><PERSON>,desenhoart", "onBoarding": false}, {"Interest": "hairstyle", "SimilarInterests": "coiffure,hairstyles,peinados", "onBoarding": false}, {"Interest": "lugares", "SimilarInterests": "places", "onBoarding": false}, {"Interest": "femme", "SimilarInterests": "donna", "onBoarding": false}, {"Interest": "thanksgiving", "SimilarInterests": "thanksgivingday", "onBoarding": false}, {"Interest": "suits", "SimilarInterests": "suit", "onBoarding": false}, {"Interest": "mirror", "SimilarInterests": "espejo", "onBoarding": false}, {"Interest": "succulents", "SimilarInterests": "suculentas", "onBoarding": false}, {"Interest": "exercicios", "SimilarInterests": "e<PERSON><PERSON><PERSON><PERSON>", "onBoarding": false}, {"Interest": "goodbye", "SimilarInterests": "outro,farewell,despedida", "onBoarding": false}, {"Interest": "costumes", "SimilarInterests": "costume", "onBoarding": false}, {"Interest": "blackandwhite", "SimilarInterests": "blancoynegro", "onBoarding": false}, {"Interest": "lookingforfriends", "SimilarInterests": "lookingfofriends,友達欲しい,lookingfriends,buscoa<PERSON><PERSON>,searching<PERSON>rfriend,s<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,a<PERSON><PERSON><PERSON><PERSON><PERSON>,lookforfriends,lookingforafriend,buscoamigo", "onBoarding": false}, {"Interest": "activities", "SimilarInterests": "activity,aktivitas,aktivite", "onBoarding": false}, {"Interest": "collection", "SimilarInterests": "colecciones,коллекция", "onBoarding": false}, {"Interest": "easter", "SimilarInterests": "easterday,páscoa,ostern,pasqua,velikonoce", "onBoarding": false}, {"Interest": "duck", "SimilarInterests": "canard,ducks", "onBoarding": false}, {"Interest": "koreanboys", "SimilarInterests": "koreaboys", "onBoarding": false}, {"Interest": "healthyeating", "SimilarInterests": "здоровоепитание,sağlıklıbeslenme", "onBoarding": false}, {"Interest": "rettungsdienst", "SimilarInterests": "ems", "onBoarding": false}, {"Interest": "simplicity", "SimilarInterests": "простоя", "onBoarding": false}, {"Interest": "color", "SimilarInterests": "colors,colour,colours", "onBoarding": false}, {"Interest": "thoughtful", "SimilarInterests": "<PERSON>ch<PERSON><PERSON><PERSON>", "onBoarding": false}, {"Interest": "landscapephotography", "SimilarInterests": "fotografíapaisajes,landschaftsfotografie", "onBoarding": false}, {"Interest": "selling", "SimilarInterests": "sale", "onBoarding": false}, {"Interest": "tarde", "SimilarInterests": "afternoon,a<PERSON><PERSON><PERSON><PERSON><PERSON>", "onBoarding": false}, {"Interest": "vidacotidiana", "SimilarInterests": "dailylife,everydaylife,cotidianidad,co<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,d<PERSON><PERSON><PERSON>,viequotidienne", "onBoarding": false}, {"Interest": "hablame", "SimilarInterests": "talktome", "onBoarding": false}, {"Interest": "санктпетербург", "SimilarInterests": "saintpetersburg,петербург", "onBoarding": false}, {"Interest": "place", "SimilarInterests": "lugar", "onBoarding": false}, {"Interest": "tatuados", "SimilarInterests": "tatuada", "onBoarding": false}, {"Interest": "decorating", "SimilarInterests": "decor,décoration,deko,decoração,украшение", "onBoarding": false}, {"Interest": "sharing", "SimilarInterests": "share", "onBoarding": false}, {"Interest": "thoughtsoftheday", "SimilarInterests": "penséedu<PERSON>r,riflessionidelgiorno,pensamientodeldia,pensierodelgiorno,pensamentosdia", "onBoarding": false}, {"Interest": "roses", "SimilarInterests": "rosas", "onBoarding": false}, {"Interest": "filologia", "SimilarInterests": "philology", "onBoarding": false}, {"Interest": "doodles", "SimilarInterests": "dibujitos,coretan", "onBoarding": false}, {"Interest": "nightwalking", "SimilarInterests": "ночныепрогулки,eveningwalks", "onBoarding": false}, {"Interest": "mentalcare", "SimilarInterests": "mentalhealthcare", "onBoarding": false}, {"Interest": "phone", "SimilarInterests": "phones", "onBoarding": false}, {"Interest": "highheels", "SimilarInterests": "tacchi,szpile", "onBoarding": false}, {"Interest": "childhood", "SimilarInterests": "enfance,infanzia", "onBoarding": false}, {"Interest": "twinflame", "SimilarInterests": "twinflames", "onBoarding": false}, {"Interest": "grief", "SimilarInterests": "bere,deuil", "onBoarding": false}, {"Interest": "irony", "SimilarInterests": "ironias", "onBoarding": false}, {"Interest": "values", "SimilarInterests": "value", "onBoarding": false}, {"Interest": "jobs", "SimilarInterests": "lavori", "onBoarding": false}, {"Interest": "goodfood", "SimilarInterests": "comidaboa", "onBoarding": false}, {"Interest": "introduction", "SimilarInterests": "kenalan", "onBoarding": false}, {"Interest": "positiveattitude", "SimilarInterests": "actitudpositiva", "onBoarding": false}, {"Interest": "знакомлюсь", "SimilarInterests": "kennen<PERSON>nen,знайомитись,gettoknowyou", "onBoarding": false}, {"Interest": "aperos", "SimilarInterests": "happyhour,apéros", "onBoarding": false}, {"Interest": "chakras", "SimilarInterests": "chakra", "onBoarding": false}, {"Interest": "romanticdate", "SimilarInterests": "citasromanticas", "onBoarding": false}, {"Interest": "questionario", "SimilarInterests": "survey,anketa,questionário", "onBoarding": false}, {"Interest": "minerals", "SimilarInterests": "minerales,mineralien,mineral", "onBoarding": false}, {"Interest": "feminine", "SimilarInterests": "femenina", "onBoarding": false}, {"Interest": "procrastinating", "SimilarInterests": "procrastination", "onBoarding": false}, {"Interest": "interaction", "SimilarInterests": "interagir,interacción,interactuando", "onBoarding": false}, {"Interest": "mothersday", "SimilarInterests": "motherday,diadasmaes", "onBoarding": false}, {"Interest": "sunflowers", "SimilarInterests": "girasol", "onBoarding": false}, {"Interest": "searching", "SimilarInterests": "поиск", "onBoarding": false}, {"Interest": "j<PERSON><PERSON><PERSON>", "SimilarInterests": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "onBoarding": false}, {"Interest": "hedgehogs", "SimilarInterests": "hedgehog", "onBoarding": false}, {"Interest": "selfdefense", "SimilarInterests": "belad<PERSON>", "onBoarding": false}, {"Interest": "goodafternoon", "SimilarInterests": "buenastarde", "onBoarding": false}, {"Interest": "father", "SimilarInterests": "fathers", "onBoarding": false}, {"Interest": "lawyer", "SimilarInterests": "<PERSON><PERSON><PERSON>", "onBoarding": false}, {"Interest": "dress", "SimilarInterests": "vestidos", "onBoarding": false}, {"Interest": "pug", "SimilarInterests": "pugs", "onBoarding": false}, {"Interest": "lutador", "SimilarInterests": "fighter", "onBoarding": false}, {"Interest": "joy", "SimilarInterests": "aleg<PERSON><PERSON>,radost,ne<PERSON>e,gioia", "onBoarding": false}, {"Interest": "fotodeldia", "SimilarInterests": "pictureoftheday,photooftheday,fotoğraffotoğraf", "onBoarding": false}, {"Interest": "lisboa", "SimilarInterests": "lisbon", "onBoarding": false}, {"Interest": "nonton", "SimilarInterests": "watching", "onBoarding": false}, {"Interest": "año2024", "SimilarInterests": "year24", "onBoarding": false}, {"Interest": "homealone", "SimilarInterests": "euem<PERSON><PERSON>", "onBoarding": false}, {"Interest": "road", "SimilarInterests": "дороги", "onBoarding": false}, {"Interest": "chihuahua", "SimilarInterests": "chihuahuas", "onBoarding": false}, {"Interest": "awakening", "SimilarInterests": "despertar,réveil", "onBoarding": false}, {"Interest": "energydrinks", "SimilarInterests": "energydrink", "onBoarding": false}, {"Interest": "nighttime", "SimilarInterests": "nachts", "onBoarding": false}, {"Interest": "<PERSON><PERSON><PERSON>", "SimilarInterests": "<PERSON><PERSON><PERSON>", "onBoarding": false}, {"Interest": "change", "SimilarInterests": "cambio", "onBoarding": false}, {"Interest": "buddha", "SimilarInterests": "buda", "onBoarding": false}, {"Interest": "foodforthought", "SimilarInterests": "foodforthoughts", "onBoarding": false}, {"Interest": "seaside", "SimilarInterests": "denizkenari", "onBoarding": false}, {"Interest": "discuter", "SimilarInterests": "discuss", "onBoarding": false}, {"Interest": "lovers", "SimilarInterests": "namorados", "onBoarding": false}, {"Interest": "potatoes", "SimilarInterests": "potato", "onBoarding": false}, {"Interest": "publicacion", "SimilarInterests": "publication", "onBoarding": false}, {"Interest": "coloringbooks", "SimilarInterests": "colouringbook", "onBoarding": false}, {"Interest": "dachshunds", "SimilarInterests": "tacskó", "onBoarding": false}, {"Interest": "loner", "SimilarInterests": "lone", "onBoarding": false}, {"Interest": "pensamentodanoite", "SimilarInterests": "pensie<PERSON><PERSON><PERSON><PERSON>,nightthoughts", "onBoarding": false}, {"Interest": "pazinterior", "SimilarInterests": "innerpeace", "onBoarding": false}, {"Interest": "nightshift", "SimilarInterests": "lavoronotturno,trabalhonoturno", "onBoarding": false}, {"Interest": "campagne", "SimilarInterests": "campania", "onBoarding": false}, {"Interest": "tips", "SimilarInterests": "consejosparati,conseils,советы,astuces", "onBoarding": false}, {"Interest": "santiagochile", "SimilarInterests": "chilesantiago", "onBoarding": false}, {"Interest": "light", "SimilarInterests": "lumi", "onBoarding": false}, {"Interest": "revolution", "SimilarInterests": "revolução", "onBoarding": false}, {"Interest": "living", "SimilarInterests": "<PERSON><PERSON><PERSON><PERSON>", "onBoarding": false}, {"Interest": "psyche", "SimilarInterests": "psiche", "onBoarding": false}, {"Interest": "heartache", "SimilarInterests": "heartaches", "onBoarding": false}, {"Interest": "morocco", "SimilarInterests": "maroc", "onBoarding": false}, {"Interest": "hamradio", "SimilarInterests": "amateurfunk", "onBoarding": false}, {"Interest": "serious", "SimilarInterests": "серьезное", "onBoarding": false}, {"Interest": "sunnyday", "SimilarInterests": "<PERSON><PERSON><PERSON><PERSON>", "onBoarding": false}, {"Interest": "bath", "SimilarInterests": "<PERSON><PERSON>", "onBoarding": false}, {"Interest": "reflecting", "SimilarInterests": "reflect,refletindo,reflexion<PERSON>o,<PERSON><PERSON><PERSON>,nachden<PERSON>,reflexiona", "onBoarding": false}, {"Interest": "fridaythe13th", "SimilarInterests": "friday13", "onBoarding": false}, {"Interest": "mantra", "SimilarInterests": "мантра", "onBoarding": false}, {"Interest": "coins", "SimilarInterests": "coin", "onBoarding": false}, {"Interest": "fortaleza", "SimilarInterests": "strength", "onBoarding": false}, {"Interest": "decorations", "SimilarInterests": "decoración", "onBoarding": false}, {"Interest": "felicidades", "SimilarInterests": "parabens,поздравления,congratulations", "onBoarding": false}, {"Interest": "annoyed", "SimilarInterests": "chateado", "onBoarding": false}, {"Interest": "location", "SimilarInterests": "lokasi", "onBoarding": false}, {"Interest": "everyone", "SimilarInterests": "to<PERSON>undo,everybody", "onBoarding": false}, {"Interest": "soirees", "SimilarInterests": "evenings", "onBoarding": false}, {"Interest": "batepapo", "SimilarInterests": "chitchat,muhabbet,kletsen", "onBoarding": false}, {"Interest": "eroticos", "SimilarInterests": "эротический", "onBoarding": false}, {"Interest": "freetime", "SimilarInterests": "tempslibre", "onBoarding": false}, {"Interest": "yerbamate", "SimilarInterests": "<PERSON><PERSON><PERSON><PERSON>", "onBoarding": false}, {"Interest": "strawberry", "SimilarInterests": "strawberries", "onBoarding": false}, {"Interest": "cockatiels", "SimilarInterests": "calopsita", "onBoarding": false}, {"Interest": "<PERSON><PERSON>r", "SimilarInterests": "romancelove", "onBoarding": false}, {"Interest": "terrariums", "SimilarInterests": "terrarium", "onBoarding": false}, {"Interest": "hurts", "SimilarInterests": "hurting", "onBoarding": false}, {"Interest": "parents", "SimilarInterests": "padres", "onBoarding": false}, {"Interest": "farmlife", "SimilarInterests": "farmeo", "onBoarding": false}, {"Interest": "moscow", "SimilarInterests": "москве", "onBoarding": false}, {"Interest": "midnight", "SimilarInterests": "geceyarısı", "onBoarding": false}, {"Interest": "apocalypse", "SimilarInterests": "apocalipse", "onBoarding": false}, {"Interest": "friendshipadvice", "SimilarInterests": "friendsadvice", "onBoarding": false}, {"Interest": "remember", "SimilarInterests": "recuerda", "onBoarding": false}, {"Interest": "dayoff", "SimilarInterests": "folga", "onBoarding": false}, {"Interest": "peaceofmind", "SimilarInterests": "pazdeespírito", "onBoarding": false}, {"Interest": "<PERSON><PERSON><PERSON>", "SimilarInterests": "coloredhair,cabelocolorido,newhaircolor", "onBoarding": false}, {"Interest": "pasión", "SimilarInterests": "paixão", "onBoarding": false}, {"Interest": "updates", "SimilarInterests": "atualizando", "onBoarding": false}, {"Interest": "graduation", "SimilarInterests": "graduación,formatura", "onBoarding": false}, {"Interest": "w<PERSON><PERSON><PERSON>", "SimilarInterests": "wrocław", "onBoarding": false}, {"Interest": "datosinteresantes", "SimilarInterests": "interestingfacts,zajímavosti,интересныефакты,zanimljivosti", "onBoarding": false}, {"Interest": "amoreal", "SimilarInterests": "reallove", "onBoarding": false}, {"Interest": "fart", "SimilarInterests": "farts", "onBoarding": false}, {"Interest": "enjoylife", "SimilarInterests": "enjoyinglife,nikmatihidup", "onBoarding": false}, {"Interest": "wishes", "SimilarInterests": "życzenia,пожелания", "onBoarding": false}, {"Interest": "yes", "SimilarInterests": "oui", "onBoarding": false}, {"Interest": "<PERSON><PERSON><PERSON>", "SimilarInterests": "sundayvibe", "onBoarding": false}, {"Interest": "phonephotography", "SimilarInterests": "mobilephotography", "onBoarding": false}, {"Interest": "advertising", "SimilarInterests": "реклама", "onBoarding": false}, {"Interest": "australian", "SimilarInterests": "aussie", "onBoarding": false}, {"Interest": "worldpeace", "SimilarInterests": "миру<PERSON>ир", "onBoarding": false}, {"Interest": "sexualhealth", "SimilarInterests": "saludsexual", "onBoarding": false}, {"Interest": "laboratory", "SimilarInterests": "laboratorio", "onBoarding": false}, {"Interest": "sicilia", "SimilarInterests": "sicily", "onBoarding": false}, {"Interest": "transportation", "SimilarInterests": "transporte", "onBoarding": false}, {"Interest": "anniversaire", "SimilarInterests": "aniversari", "onBoarding": false}, {"Interest": "castle", "SimilarInterests": "chateau", "onBoarding": false}, {"Interest": "october", "SimilarInterests": "octubre", "onBoarding": false}, {"Interest": "choices", "SimilarInterests": "choose,escolhas,pilihan", "onBoarding": false}, {"Interest": "sketchbook", "SimilarInterests": "sketchbooking", "onBoarding": false}, {"Interest": "peaceful", "SimilarInterests": "peacefulness", "onBoarding": false}, {"Interest": "sensitivity", "SimilarInterests": "sensibilidad", "onBoarding": false}, {"Interest": "daughter", "SimilarInterests": "filha", "onBoarding": false}, {"Interest": "thinker", "SimilarInterests": "thinkering", "onBoarding": false}, {"Interest": "alguienconquienhablar", "SimilarInterests": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,someonetotalkto", "onBoarding": false}, {"Interest": "serenity", "SimilarInterests": "huzur", "onBoarding": false}, {"Interest": "trend", "SimilarInterests": "trending,tren,gündem", "onBoarding": false}, {"Interest": "planting", "SimilarInterests": "plantar", "onBoarding": false}, {"Interest": "bestie", "SimilarInterests": "besties", "onBoarding": false}, {"Interest": "mulher<PERSON>", "SimilarInterests": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,womanpower", "onBoarding": false}, {"Interest": "devotional", "SimilarInterests": "devotion", "onBoarding": false}, {"Interest": "womenday", "SimilarInterests": "womandays", "onBoarding": false}, {"Interest": "pilzesammeln", "SimilarInterests": "grzybobranie", "onBoarding": false}, {"Interest": "ironic", "SimilarInterests": "ironico", "onBoarding": false}, {"Interest": "wallpaper", "SimilarInterests": "wallpapers,fondosdepantalla", "onBoarding": false}, {"Interest": "acceptance", "SimilarInterests": "acceptation,aceitação", "onBoarding": false}, {"Interest": "frasesaleat<PERSON>s", "SimilarInterests": "randomquotes", "onBoarding": false}, {"Interest": "kitchen", "SimilarInterests": "кухня,cosina", "onBoarding": false}, {"Interest": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SimilarInterests": "hotweather", "onBoarding": false}, {"Interest": "celebrities", "SimilarInterests": "celebrity", "onBoarding": false}, {"Interest": "psiquiatría", "SimilarInterests": "psichiatria", "onBoarding": false}, {"Interest": "narcissistic", "SimilarInterests": "narcisista", "onBoarding": false}, {"Interest": "character", "SimilarInterests": "<PERSON><PERSON><PERSON>", "onBoarding": false}, {"Interest": "locura", "SimilarInterests": "madness", "onBoarding": false}, {"Interest": "latenighttalks", "SimilarInterests": "conversarmadrugada", "onBoarding": false}, {"Interest": "questõesdodia", "SimilarInterests": "dailyquestion", "onBoarding": false}, {"Interest": "motivationalquote", "SimilarInterests": "frasimotivazionali,frasesmotivacionais", "onBoarding": false}, {"Interest": "concurs<PERSON><PERSON><PERSON><PERSON><PERSON>", "SimilarInterests": "concursop<PERSON><PERSON>o", "onBoarding": false}, {"Interest": "desire", "SimilarInterests": "desires,desiderio", "onBoarding": false}, {"Interest": "fotico", "SimilarInterests": "fotinha,fotito", "onBoarding": false}, {"Interest": "moveon", "SimilarInterests": "<PERSON>on", "onBoarding": false}, {"Interest": "anecdote", "SimilarInterests": "anécdo<PERSON>", "onBoarding": false}, {"Interest": "squirrels", "SimilarInterests": "squirrel", "onBoarding": false}, {"Interest": "chica", "SimilarInterests": "cewe", "onBoarding": false}, {"Interest": "soapmaking", "SimilarInterests": "soap", "onBoarding": false}, {"Interest": "münchen", "SimilarInterests": "munich", "onBoarding": false}, {"Interest": "playlist", "SimilarInterests": "playlists", "onBoarding": false}, {"Interest": "focus", "SimilarInterests": "foco", "onBoarding": false}, {"Interest": "riresensemble", "SimilarInterests": "rireensemble", "onBoarding": false}, {"Interest": "versek", "SimilarInterests": "verse,verso", "onBoarding": false}, {"Interest": "harmonie", "SimilarInterests": "harmony,sintonia", "onBoarding": false}, {"Interest": "moving", "SimilarInterests": "moiv", "onBoarding": false}, {"Interest": "morality", "SimilarInterests": "morals", "onBoarding": false}, {"Interest": "posting", "SimilarInterests": "postando,publicación", "onBoarding": false}, {"Interest": "moviequotes", "SimilarInterests": "frasescine", "onBoarding": false}, {"Interest": "filosofard<PERSON>vida", "SimilarInterests": "filosiadevida", "onBoarding": false}, {"Interest": "жизньвмоменте", "SimilarInterests": "vivreaupresent", "onBoarding": false}, {"Interest": "musicvideos", "SimilarInterests": "видиоклипы", "onBoarding": false}, {"Interest": "viveravida", "SimilarInterests": "livelife", "onBoarding": false}, {"Interest": "shortstories", "SimilarInterests": "cuentoscortos", "onBoarding": false}, {"Interest": "height", "SimilarInterests": "altura", "onBoarding": false}, {"Interest": "presente", "SimilarInterests": "present", "onBoarding": false}, {"Interest": "diade<PERSON><PERSON><PERSON>", "SimilarInterests": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,diadelos<PERSON><PERSON><PERSON>", "onBoarding": false}, {"Interest": "earthquakes", "SimilarInterests": "sismos", "onBoarding": false}, {"Interest": "melancholic", "SimilarInterests": "melancholia", "onBoarding": false}, {"Interest": "delusional", "SimilarInterests": "delusion", "onBoarding": false}, {"Interest": "achievement", "SimilarInterests": "достижение", "onBoarding": false}, {"Interest": "recommendations", "SimilarInterests": "recos", "onBoarding": false}, {"Interest": "worldcup", "SimilarInterests": "mundial", "onBoarding": false}, {"Interest": "amourinconditionnel", "SimilarInterests": "unconditionallove", "onBoarding": false}, {"Interest": "fairytale", "SimilarInterests": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "onBoarding": false}, {"Interest": "skills", "SimilarInterests": "skill", "onBoarding": false}, {"Interest": "courage", "SimilarInterests": "co<PERSON>m", "onBoarding": false}, {"Interest": "routine", "SimilarInterests": "rutina", "onBoarding": false}, {"Interest": "naturelife", "SimilarInterests": "naturalezadelavida", "onBoarding": false}, {"Interest": "schla<PERSON><PERSON>", "SimilarInterests": "sleepless,nuitblanche,sleeplessnight,desvelados", "onBoarding": false}, {"Interest": "behavior", "SimilarInterests": "comportamentos,comportament", "onBoarding": false}, {"Interest": "sayings", "SimilarInterests": "اقوال", "onBoarding": false}, {"Interest": "amizadeseamor", "SimilarInterests": "amici<PERSON><PERSON><PERSON>,friendlove,friendshipandlove", "onBoarding": false}, {"Interest": "profilepicture", "SimilarInterests": "fotodeperfil", "onBoarding": false}, {"Interest": "feelgood", "SimilarInterests": "feelinggood", "onBoarding": false}, {"Interest": "panamacity", "SimilarInterests": "panamá", "onBoarding": false}, {"Interest": "monuments", "SimilarInterests": "den<PERSON><PERSON>", "onBoarding": false}, {"Interest": "others", "SimilarInterests": "<PERSON><PERSON><PERSON><PERSON>", "onBoarding": false}, {"Interest": "infieles", "SimilarInterests": "cheater,cheaters", "onBoarding": false}, {"Interest": "dominicanrepublic", "SimilarInterests": "dominicana", "onBoarding": false}, {"Interest": "olimpiadas", "SimilarInterests": "olympic,olympicgames,olimpíadas,olimpiyatoyunları,olympischespiele", "onBoarding": false}, {"Interest": "gatonegro", "SimilarInterests": "gatopreto", "onBoarding": false}, {"Interest": "decision", "SimilarInterests": "decisions", "onBoarding": false}, {"Interest": "wakeup", "SimilarInterests": "acordar", "onBoarding": false}, {"Interest": "musicaantigas", "SimilarInterests": "vintagemusic", "onBoarding": false}, {"Interest": "domandenotturne", "SimilarInterests": "questions<PERSON>oir", "onBoarding": false}, {"Interest": "reflexiónsvarias", "SimilarInterests": "reflexionesvarias", "onBoarding": false}, {"Interest": "pregu<PERSON>", "SimilarInterests": "flojera,laziness,pereza", "onBoarding": false}, {"Interest": "<PERSON><PERSON><PERSON><PERSON>", "SimilarInterests": "messaging", "onBoarding": false}, {"Interest": "cravings", "SimilarInterests": "craving", "onBoarding": false}, {"Interest": "moodoftheday", "SimilarInterests": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "onBoarding": false}, {"Interest": "someone", "SimilarInterests": "alguem", "onBoarding": false}, {"Interest": "anger", "SimilarInterests": "en<PERSON><PERSON>,colère", "onBoarding": false}, {"Interest": "tonight", "SimilarInterests": "stasera", "onBoarding": false}, {"Interest": "proverbs", "SimilarInterests": "proverbio,proverbe,proverb,provérbios", "onBoarding": false}, {"Interest": "single30", "SimilarInterests": "singleat30", "onBoarding": false}, {"Interest": "photobomb", "SimilarInterests": "photoboom", "onBoarding": false}, {"Interest": "hindip<PERSON><PERSON>", "SimilarInterests": "<PERSON><PERSON><PERSON><PERSON>", "onBoarding": false}, {"Interest": "jul", "SimilarInterests": "july", "onBoarding": false}, {"Interest": "relaxed", "SimilarInterests": "<PERSON><PERSON><PERSON><PERSON>", "onBoarding": false}, {"Interest": "sakura", "SimilarInterests": "cherryblossom", "onBoarding": false}, {"Interest": "masks", "SimilarInterests": "mask", "onBoarding": false}, {"Interest": "fail", "SimilarInterests": "failure", "onBoarding": false}, {"Interest": "mandala", "SimilarInterests": "mandalaart", "onBoarding": false}, {"Interest": "attractive", "SimilarInterests": "atractivos", "onBoarding": false}, {"Interest": "citylights", "SimilarInterests": "citylight", "onBoarding": false}, {"Interest": "bucurești", "SimilarInterests": "bucharest", "onBoarding": false}, {"Interest": "k<PERSON>zlar", "SimilarInterests": "девчонки", "onBoarding": false}, {"Interest": "luck", "SimilarInterests": "suerte", "onBoarding": false}, {"Interest": "questionstonight", "SimilarInterests": "preguntanoche", "onBoarding": false}, {"Interest": "comments", "SimilarInterests": "comenta", "onBoarding": false}, {"Interest": "a3", "SimilarInterests": "a3r", "onBoarding": true}, {"Interest": "problema", "SimilarInterests": "проблема", "onBoarding": false}, {"Interest": "idulfitri", "SimilarInterests": "e<PERSON><PERSON><PERSON>", "onBoarding": false}, {"Interest": "fobia", "SimilarInterests": "phobia", "onBoarding": true}, {"Interest": "partnership", "SimilarInterests": "partnerschaft", "onBoarding": false}, {"Interest": "interests", "SimilarInterests": "інтереси", "onBoarding": false}, {"Interest": "destino", "SimilarInterests": "destination", "onBoarding": false}, {"Interest": "messages", "SimilarInterests": "mensagens", "onBoarding": false}, {"Interest": "mylove", "SimilarInterests": "amoremio", "onBoarding": false}, {"Interest": "dailymotivation", "SimilarInterests": "conseildujour", "onBoarding": false}, {"Interest": "atmosphere", "SimilarInterests": "атмосфера,atmospheric", "onBoarding": false}, {"Interest": "womencircles", "SimilarInterests": "womancircles", "onBoarding": false}, {"Interest": "election", "SimilarInterests": "eleições", "onBoarding": false}, {"Interest": "september", "SimilarInterests": "septiembre", "onBoarding": false}, {"Interest": "<PERSON><PERSON><PERSON>", "SimilarInterests": "ban<PERSON><PERSON>", "onBoarding": false}, {"Interest": "beziehungsfragen", "SimilarInterests": "relationshipquestions", "onBoarding": false}, {"Interest": "threads", "SimilarInterests": "thread", "onBoarding": false}, {"Interest": "searchingforlove", "SimilarInterests": "<PERSON><PERSON><PERSON><PERSON><PERSON>,finding<PERSON>rl<PERSON>,findforlove", "onBoarding": false}, {"Interest": "notes", "SimilarInterests": "записки", "onBoarding": false}, {"Interest": "donation", "SimilarInterests": "donate", "onBoarding": false}, {"Interest": "noeldecoration", "SimilarInterests": "christmasdecorationss", "onBoarding": false}, {"Interest": "nightmares", "SimilarInterests": "nightmare", "onBoarding": false}, {"Interest": "sundaymorning", "SimilarInterests": "dimanchematin", "onBoarding": false}, {"Interest": "feelingblue", "SimilarInterests": "feelblue", "onBoarding": false}, {"Interest": "earning", "SimilarInterests": "earn", "onBoarding": false}, {"Interest": "vuivẻ", "SimilarInterests": "веселе", "onBoarding": false}, {"Interest": "adorable", "SimilarInterests": "умиления", "onBoarding": false}, {"Interest": "missyou", "SimilarInterests": "rind<PERSON><PERSON>n,saudades", "onBoarding": false}, {"Interest": "frases<PERSON>as", "SimilarInterests": "frasesbonitas,bellecitation", "onBoarding": false}, {"Interest": "fotoaleatório", "SimilarInterests": "fotoalea", "onBoarding": false}, {"Interest": "dialogue", "SimilarInterests": "диал<PERSON>г", "onBoarding": false}, {"Interest": "pijama", "SimilarInterests": "pajamas", "onBoarding": false}, {"Interest": "autumnvibes", "SimilarInterests": "<PERSON><PERSON><PERSON>", "onBoarding": false}, {"Interest": "retailwork", "SimilarInterests": "retail", "onBoarding": false}, {"Interest": "blessings", "SimilarInterests": "bendiciones", "onBoarding": false}, {"Interest": "chainmail", "SimilarInterests": "chainmaille", "onBoarding": false}, {"Interest": "gez<PERSON>i", "SimilarInterests": "lo<PERSON><PERSON>,szabadidő,休閒", "onBoarding": false}, {"Interest": "images", "SimilarInterests": "imagens,obrazy,immagini", "onBoarding": false}, {"Interest": "historiasdevida", "SimilarInterests": "lifestory", "onBoarding": false}, {"Interest": "funnyvideo", "SimilarInterests": "смешныевидео", "onBoarding": false}, {"Interest": "makeover", "SimilarInterests": "mudançadevisual", "onBoarding": false}, {"Interest": "songoftheday", "SimilarInterests": "canciondeldia", "onBoarding": false}, {"Interest": "سؤالوجواب", "SimilarInterests": "o<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,o<PERSON>z<PERSON><PERSON><PERSON><PERSON><PERSON>i", "onBoarding": false}, {"Interest": "insight", "SimilarInterests": "инсайт", "onBoarding": false}, {"Interest": "weihnachtszeit", "SimilarInterests": "<PERSON><PERSON><PERSON><PERSON>,natalino", "onBoarding": false}, {"Interest": "trabajointerno", "SimilarInterests": "lavorointeriore", "onBoarding": false}, {"Interest": "urbanart", "SimilarInterests": "arteurbano", "onBoarding": false}, {"Interest": "emocionesfuertes", "SimilarInterests": "emoz<PERSON>ifort<PERSON>", "onBoarding": false}, {"Interest": "<PERSON><PERSON><PERSON>", "SimilarInterests": "enamorar,enamoramiento", "onBoarding": false}, {"Interest": "bonnesoirée", "SimilarInterests": "<PERSON><PERSON><PERSON><PERSON>,buonaserata", "onBoarding": false}, {"Interest": "injuries", "SimilarInterests": "injury", "onBoarding": false}, {"Interest": "mondayblue", "SimilarInterests": "mondayblues", "onBoarding": false}, {"Interest": "lovequotes", "SimilarInterests": "frasedeamor", "onBoarding": false}, {"Interest": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SimilarInterests": "pensam,pensez", "onBoarding": false}, {"Interest": "review", "SimilarInterests": "criticas", "onBoarding": false}, {"Interest": "streets", "SimilarInterests": "sokak", "onBoarding": false}, {"Interest": "<PERSON><PERSON>r", "SimilarInterests": "<PERSON><PERSON><PERSON><PERSON>", "onBoarding": false}, {"Interest": "เพลงโปรด", "SimilarInterests": "cancionfavorita", "onBoarding": false}, {"Interest": "didyouknow", "SimilarInterests": "sabiasque", "onBoarding": false}, {"Interest": "june", "SimilarInterests": "junho", "onBoarding": false}, {"Interest": "youth", "SimilarInterests": "jeunesse", "onBoarding": false}, {"Interest": "bathroom", "SimilarInterests": "baño", "onBoarding": false}, {"Interest": "aforisma", "SimilarInterests": "aforismos", "onBoarding": false}, {"Interest": "diadasmulheres", "SimilarInterests": "kadınlargünü", "onBoarding": false}, {"Interest": "вопросвечера", "SimilarInterests": "pytaniwieczora", "onBoarding": false}, {"Interest": "красотамира", "SimilarInterests": "beautyoftheworld", "onBoarding": false}, {"Interest": "profession", "SimilarInterests": "профессия,profesiones", "onBoarding": false}, {"Interest": "inlove", "SimilarInterests": "apaixonado", "onBoarding": false}, {"Interest": "oneday", "SimilarInterests": "sehari", "onBoarding": false}, {"Interest": "be<PERSON><PERSON>", "SimilarInterests": "belem", "onBoarding": false}, {"Interest": "happylife", "SimilarInterests": "счастливаяжизнь,felizcomavida", "onBoarding": false}, {"Interest": "brothers", "SimilarInterests": "<PERSON><PERSON><PERSON>", "onBoarding": false}, {"Interest": "amicalement", "SimilarInterests": "<PERSON><PERSON><PERSON>", "onBoarding": false}, {"Interest": "mythoughts", "SimilarInterests": "<PERSON><PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON><PERSON>,meuspensamentos", "onBoarding": false}, {"Interest": "bookquotes", "SimilarInterests": "kitapalıntıları,citazionidalibri,frasesdelibros,kitaplardanalıntılar", "onBoarding": false}, {"Interest": "iamme", "SimilarInterests": "<PERSON><PERSON><PERSON><PERSON>", "onBoarding": false}, {"Interest": "singlechat", "SimilarInterests": "singleschat", "onBoarding": false}, {"Interest": "volcan", "SimilarInterests": "volcano", "onBoarding": false}, {"Interest": "morningthoughts", "SimilarInterests": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "onBoarding": false}, {"Interest": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SimilarInterests": "salamken<PERSON><PERSON>", "onBoarding": false}, {"Interest": "mensagemdodia", "SimilarInterests": "messageoftheday", "onBoarding": false}, {"Interest": "8m", "SimilarInterests": "8<PERSON><PERSON>", "onBoarding": false}, {"Interest": "dailyadvice", "SimilarInterests": "conselhosdi<PERSON><PERSON><PERSON>", "onBoarding": false}, {"Interest": "postagens", "SimilarInterests": "posteos", "onBoarding": false}, {"Interest": "santaclaus", "SimilarInterests": "<PERSON>ih<PERSON>sman<PERSON>", "onBoarding": false}, {"Interest": "внутренниймир", "SimilarInterests": "innerworld", "onBoarding": false}, {"Interest": "lindodia", "SimilarInterests": "<PERSON><PERSON><PERSON><PERSON>", "onBoarding": false}, {"Interest": "reasons", "SimilarInterests": "причины", "onBoarding": false}, {"Interest": "testimonio", "SimilarInterests": "testi", "onBoarding": false}, {"Interest": "myworld", "SimilarInterests": "mi<PERSON>o", "onBoarding": false}, {"Interest": "teag", "SimilarInterests": "teatime", "onBoarding": false}, {"Interest": "birthday31", "SimilarInterests": "birthday20,birthday26,birthday23,birthday24", "onBoarding": false}, {"Interest": "girlsmode", "SimilarInterests": "girlpower", "onBoarding": false}, {"Interest": "gorgeous", "SimilarInterests": "lindoo", "onBoarding": false}, {"Interest": "temuco", "SimilarInterests": "temu", "onBoarding": false}, {"Interest": "letter", "SimilarInterests": "surat", "onBoarding": false}, {"Interest": "selfmotivation", "SimilarInterests": "motivacionpersonal", "onBoarding": false}, {"Interest": "זוגיות", "SimilarInterests": "couplegoals", "onBoarding": false}, {"Interest": "know", "SimilarInterests": "conoscere", "onBoarding": false}, {"Interest": "jajan", "SimilarInterests": "<PERSON><PERSON><PERSON>", "onBoarding": false}, {"Interest": "datingadventure", "SimilarInterests": "re<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "onBoarding": false}, {"Interest": "cabelocurto", "SimilarInterests": "pelocorto,shorthair", "onBoarding": false}, {"Interest": "sceneryinatural", "SimilarInterests": "scenery,пейзаж", "onBoarding": false}, {"Interest": "2000", "SimilarInterests": "2000s", "onBoarding": false}, {"Interest": "ny", "SimilarInterests": "nyc", "onBoarding": false}, {"Interest": "ma<PERSON>i", "SimilarInterests": "tuesday", "onBoarding": false}, {"Interest": "newyears2024", "SimilarInterests": "newyear24", "onBoarding": false}, {"Interest": "temporal", "SimilarInterests": "timetravel", "onBoarding": false}, {"Interest": "lebensfreude", "SimilarInterests": "порадидляжиття", "onBoarding": false}, {"Interest": "angels", "SimilarInterests": "angeln", "onBoarding": false}, {"Interest": "seman<PERSON><PERSON><PERSON>", "SimilarInterests": "goodmorningvibes", "onBoarding": false}, {"Interest": "lebon<PERSON><PERSON>", "SimilarInterests": "salutare,ciaoo,hallo,salut,bonjour", "onBoarding": false}, {"Interest": "moodswing", "SimilarInterests": "moodswings", "onBoarding": false}, {"Interest": "noia", "SimilarInterests": "anxiety", "onBoarding": false}, {"Interest": "gemstone", "SimilarInterests": "gemstones", "onBoarding": false}, {"Interest": "erkekler", "SimilarInterests": "homens,hombres,malen", "onBoarding": false}, {"Interest": "catlovers", "SimilarInterests": "catsandlove,catsarelove,catlover,gatolover", "onBoarding": false}, {"Interest": "relationshipgoal", "SimilarInterests": "relationshipgoals", "onBoarding": false}, {"Interest": "bycicling", "SimilarInterests": "rad<PERSON><PERSON>", "onBoarding": false}, {"Interest": "bakery", "SimilarInterests": "panadería", "onBoarding": false}, {"Interest": "spiritualism", "SimilarInterests": "espiritualista", "onBoarding": false}, {"Interest": "cryptid", "SimilarInterests": "cryptids", "onBoarding": false}, {"Interest": "mbti", "SimilarInterests": "mtbi", "onBoarding": true}, {"Interest": "shayar", "SimilarInterests": "shayri,shayari", "onBoarding": false}, {"Interest": "juegosdepc", "SimilarInterests": "jogosdepc", "onBoarding": true}, {"Interest": "paint", "SimilarInterests": "melukis", "onBoarding": false}, {"Interest": "romane", "SimilarInterests": "romanace", "onBoarding": false}, {"Interest": "doubts", "SimilarInterests": "doubt", "onBoarding": false}, {"Interest": "sneaker", "SimilarInterests": "sneakers", "onBoarding": false}, {"Interest": "songs", "SimilarInterests": "lagu,song,chanson", "onBoarding": false}, {"Interest": "thesims4", "SimilarInterests": "sims", "onBoarding": true}, {"Interest": "knitting", "SimilarInterests": "tejer,tricot", "onBoarding": false}, {"Interest": "pop", "SimilarInterests": "پاپ,popculture", "onBoarding": true}]